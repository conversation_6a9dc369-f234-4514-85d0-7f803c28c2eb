# Hướng dẫn Migration từ 3 Plugin riêng lẻ sang AccuFlow Extensions

## Tổng quan

AccuFlow Extensions v3.0.0 gộp 3 plugin riêng lẻ thành 1 plugin duy nhất:
- AccuFlow - Google Sheets Integration
- Quick Payment Buttons  
- WooCommerce Linked Products Buttons

## Bước 1: Backup dữ liệu

Trước khi migration, hãy backup:
- Database WordPress
- Thư mục `/wp-content/plugins/`
- <PERSON>ác cài đặt plugin hiện tại

## Bước 2: Xu<PERSON>t cài đặt hiện tại (Tùy chọn)

### Google Sheets Integration
```sql
SELECT * FROM wp_options WHERE option_name LIKE 'accuflow_%';
```

### Quick Payment Buttons
```sql
SELECT * FROM wp_options WHERE option_name LIKE 'qpb_%';
```

### Linked Products Buttons
```sql
SELECT * FROM wp_options WHERE option_name LIKE 'choacc_linked_buttons_%';
```

## Bước 3: Deactivate các plugin cũ

1. Vào **Plugins > Installed Plugins**
2. Deactivate (KHÔNG XÓA) các plugin:
   - AccuFlow - Google Sheets Integration
   - Quick Payment Buttons
   - Choacc.com - Linked Products Buttons

## Bước 4: Cài đặt AccuFlow Extensions

1. Upload thư mục `Accuflow-sheets-integration` vào `/wp-content/plugins/`
2. Đổi tên thành `accuflow-extensions` (tùy chọn)
3. Activate plugin mới

## Bước 5: Kiểm tra cài đặt

### Automatic Migration
Plugin mới sẽ tự động nhận diện các cài đặt cũ:

- ✅ **Google Sheets settings**: Tự động migrate
- ✅ **Quick Payment CSS**: Tự động migrate  
- ✅ **Linked Products settings**: Tự động migrate
- ✅ **Product meta data**: Giữ nguyên

### Manual Check
1. Vào **WooCommerce > AccuFlow Extensions**
2. Kiểm tra từng tab:
   - **Google Sheets Integration**: Verify API settings
   - **Quick Payment Buttons**: Check CSS customization
   - **Linked Products Buttons**: Verify group label và CSS

## Bước 6: Test chức năng

### Google Sheets Integration
- [ ] Test connection to Google Sheets
- [ ] Verify order fulfillment
- [ ] Check email sending

### Quick Payment Buttons
- [ ] Kiểm tra nút hiển thị trên product page
- [ ] Test "Mua ngay" functionality
- [ ] Test "Thêm vào giỏ" functionality

### Linked Products Buttons
- [ ] Kiểm tra cross-sell products hiển thị
- [ ] Verify custom button names
- [ ] Test button styling

## Bước 7: Cleanup (Sau khi test thành công)

1. **Xóa plugin cũ** (chỉ sau khi đã test kỹ):
   ```
   - Quick Payment Buttons/
   - WooCommerce Linked Products Buttons/
   ```

2. **Giữ lại backup** ít nhất 1 tuần

## Troubleshooting

### Vấn đề thường gặp

**1. Settings không migrate**
```php
// Check trong wp_options table
SELECT * FROM wp_options WHERE option_name IN (
    'accuflow_settings',
    'qpb_custom_css', 
    'choacc_linked_buttons_custom_css',
    'choacc_linked_buttons_group_label'
);
```

**2. CSS không hiển thị**
- Kiểm tra tab Quick Payment Buttons
- Re-save CSS settings
- Clear cache

**3. Nút không hiển thị**
- Kiểm tra WooCommerce active
- Verify product có cross-sell products
- Check theme compatibility

**4. Google Sheets không kết nối**
- Re-enter Service Account credentials
- Test connection trong admin
- Check Google API permissions

### Debug Mode

Bật debug mode để kiểm tra:
```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Sau đó vào **Tools > AccuFlow Test** để chạy diagnostic.

## Rollback Plan

Nếu có vấn đề:

1. Deactivate AccuFlow Extensions
2. Activate lại các plugin cũ
3. Restore từ backup nếu cần
4. Báo cáo lỗi để được hỗ trợ

## Hỗ trợ

- Email: <EMAIL>
- Documentation: https://accuflow.com/docs
- GitHub Issues: [Link repository]

## Lưu ý quan trọng

- ⚠️ **KHÔNG XÓA** plugin cũ ngay lập tức
- ✅ **LUÔN BACKUP** trước khi migration  
- 🧪 **TEST KỸ** trên staging site trước
- 📞 **LIÊN HỆ** nếu cần hỗ trợ migration
