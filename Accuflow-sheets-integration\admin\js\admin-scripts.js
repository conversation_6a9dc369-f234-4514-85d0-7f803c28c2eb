jQuery(document).ready(function($) {
    // KHAI BÁO BIẾN $modal TẠI ĐÂY để nó có phạm vi toàn cục trong script này
    var $modal = $('#accuflow-email-settings-modal');

    // Tab functionality for main admin page
    $('.accuflow-tab-button').on('click', function() {
        var tabId = $(this).data('tab');

        $('.accuflow-tab-button').removeClass('active');
        $(this).addClass('active');

        $('.accuflow-tab-content').removeClass('active').hide();
        $('#accuflow-tab-' + tabId).addClass('active').show();

        // Update URL hash to reflect current tab
        if (history.pushState) {
            history.pushState(null, null, '#' + tabId);
        } else {
            window.location.hash = '#' + tabId;
        }

        // Load product list if on that tab and not loaded yet
        if (tabId === 'product-links' && !$('#product-list-body').data('loaded')) {
            loadProductList(accuflow_ajax_object.current_s_query, accuflow_ajax_object.current_paged);
            $('#product-list-body').data('loaded', true);
        }

        // Initialize color pickers when on WC Emails tab
        if (tabId === 'wc-emails') {
            // Delay initialization slightly to ensure elements are visible
            setTimeout(function() {
                $('.accuflow-color-picker').each(function() {
                    // Check if Iris has already been initialized
                    if (!$(this).data('iris')) {
                        $(this).iris({
                            hide: true,
                            palettes: true,
                            change: function(event, ui) {
                                $(this).css({ backgroundColor: ui.color.toString() });
                            }
                        });
                    }
                });
            }, 100);
        }

        // Handle specific tab initializations
        if (tabId === 'quick-payment') {
            // Initialize code editor for Quick Payment CSS if available
            if (typeof CodeMirror !== 'undefined' && $('#qpb_custom_css').length) {
                if (!$('#qpb_custom_css').data('codemirror-initialized')) {
                    CodeMirror.fromTextArea(document.getElementById('qpb_custom_css'), {
                        mode: 'css',
                        lineNumbers: true,
                        theme: 'default'
                    });
                    $('#qpb_custom_css').data('codemirror-initialized', true);
                }
            }
        }

        if (tabId === 'linked-products') {
            // Initialize code editor for Linked Products CSS if available
            if (typeof CodeMirror !== 'undefined' && $('#choacc_linked_buttons_custom_css').length) {
                if (!$('#choacc_linked_buttons_custom_css').data('codemirror-initialized')) {
                    CodeMirror.fromTextArea(document.getElementById('choacc_linked_buttons_custom_css'), {
                        mode: 'css',
                        lineNumbers: true,
                        theme: 'default'
                    });
                    $('#choacc_linked_buttons_custom_css').data('codemirror-initialized', true);
                }
            }
        }
    });

    // Initial tab selection - use the active tab set by PHP
    var initialTab = window.location.hash.substring(1);
    var targetTabButton = null;

    if (initialTab && $('.accuflow-tab-button[data-tab="' + initialTab + '"]').length) {
        // Hash in URL takes priority
        targetTabButton = $('.accuflow-tab-button[data-tab="' + initialTab + '"]');
        targetTabButton.trigger('click');
    } else {
        // Use the active tab set by PHP (from URL parameter or default)
        // No need to trigger click as PHP already set the correct active state
    }

    $('html, body').animate({ scrollTop: 0 }, 'fast');

    // Function to toggle visibility of config fields for product rows
    function toggleFulfillmentConfig(productId, method) {
        var $row = $('#product-row-' + productId);
        $row.find('.accuflow-fulfillment-config').hide();
        $row.find('.accuflow-sheet-config-buttons, .accuflow-api-config-buttons').hide();

        if (method === 'google_sheet') {
            $row.find('.accuflow-sheet-config').show();
            $row.find('.accuflow-sheet-config-buttons').show();
        } else if (method === 'api_call') {
            $row.find('.accuflow-api-config').show();
            $row.find('.accuflow-api-config-buttons').show();
            var $customLinkCheckbox = $row.find('.accuflow-api-input-custom-link');
            var $regexField = $row.find('.accuflow-api-custom-link-regex-field');
            if ($customLinkCheckbox.is(':checked')) {
                $regexField.show();
            } else {
                $regexField.hide();
            }
        }
    }

    // Function to load product list via AJAX
    function loadProductList(searchQuery, pageNum) {
        var $productListBody = $('#product-list-body');
        $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span class="spinner is-active" style="float:none; vertical-align: middle;"></span> Đang tải sản phẩm...</td></tr>');

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_get_product_list_ajax',
                security: accuflow_ajax_object.get_product_list_nonce,
                s: searchQuery,
                paged: pageNum
            },
            success: function(response) {
                if (response.success) {
                    $productListBody.html(response.data.rows);
                    $('.tablenav-pages').html(response.data.pagination);

                    $('.accuflow-fulfillment-method').each(function() {
                        var productId = $(this).data('product-id');
                        var method = $(this).val();
                        toggleFulfillmentConfig(productId, method);
                    });

                } else {
                    $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span style="color: red;">Lỗi khi tải sản phẩm: ' + response.data + '</span></td></tr>');
                }
            },
            error: function() {
                $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span style="color: red;">Lỗi không xác định khi tải sản phẩm.</span></td></tr>');
            },
            complete: function() {
                // Ensure all dynamically loaded elements also have correct event handlers if needed
            }
        });
    }

    // Function to toggle fulfillment config display
    function toggleFulfillmentConfig(productId, method) {
        var $row = $('select[data-product-id="' + productId + '"]').closest('tr');
        var $googleConfig = $row.find('.accuflow-google-sheet-config');
        var $apiConfig = $row.find('.accuflow-api-config');

        // Hide all configs first
        $googleConfig.hide();
        $apiConfig.hide();

        // Show relevant config
        if (method === 'google_sheet') {
            $googleConfig.show();
        } else if (method === 'api_call') {
            $apiConfig.show();
        }
    }

    // Handle change event for fulfillment method dropdown (delegated for dynamic content)
    $(document).on('change', '.accuflow-fulfillment-method-select', function() {
        var productId = $(this).data('product-id');
        var method = $(this).val();
        toggleFulfillmentConfig(productId, method);
        
        // Auto-save fulfillment method immediately
        var $row = $(this).closest('tr');
        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_auto_save_fulfillment_method',
                security: accuflow_ajax_object.save_product_links_nonce,
                product_id: productId,
                fulfillment_method: method
            },
            success: function(response) {
                if (response.success) {
                    // Show success indicator briefly
                    $row.find('.fulfillment-method-status').remove();
                    $row.find('.accuflow-fulfillment-method-select').after('<span class="fulfillment-method-status" style="color: green; margin-left: 5px;">✓ Đã lưu</span>');
                    setTimeout(function() {
                        $('.fulfillment-method-status').fadeOut();
                    }, 2000);
                }
            }
        });
    });

    // Handle change for input field checkboxes (delegated event)
    $(document).on('change', '.accuflow-api-input-field-checkbox', function() {
        var fieldName = $(this).data('field');
        var $apiConfig = $(this).closest('.accuflow-api-config');
        var $placeholderField = $apiConfig.find('.placeholder-field[data-field="' + fieldName + '"]');

        if ($(this).is(':checked')) {
            $placeholderField.slideDown();
        } else {
            $placeholderField.slideUp();
        }
    });

    // Test Read (Google Sheet)
    $(document).on('click', '.accuflow-sheets-test-link', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var tabGid = button.closest('tr').find('.accuflow-sheets-tab-gid').val();
        var testColNumber = $('#test-col-' + productId).val() || 4; // Default to column 4 (Status)
        var testStatusName = $('#test-status-' + productId).val() || 'Available'; // Default status
        var resultSpan = $('#test-result-' + productId);

        // Debug: Log what values we're sending
        console.log('Test Debug - Product ID:', productId);
        console.log('Test Debug - Column Number:', testColNumber);
        console.log('Test Debug - Status Name:', testStatusName);

        resultSpan.html('<span class="spinner is-active"></span> Đang kiểm tra cột ' + testColNumber + ' với trạng thái "' + testStatusName + '"...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_product_link_ajax',
                security: accuflow_ajax_object.test_product_link_nonce,
                product_id: productId,
                tab_gid: tabGid,
                test_col_number: testColNumber,
                test_status_name: testStatusName
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi kiểm tra đọc.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Write connection log (Google Sheet)
    $(document).on('click', '.accuflow-sheets-write-log-link', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var productSku = button.data('product-sku');
        var tabGid = button.closest('tr').find('.accuflow-sheets-tab-gid').val();
        var resultSpan = $('#test-result-' + productId);

        resultSpan.html('<span class="spinner is-active"></span> Đang ghi log...</span>');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_write_connection_log_ajax',
                security: accuflow_ajax_object.write_connection_log_nonce,
                product_id: productId,
                tab_gid: tabGid,
                product_sku: productSku
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi ghi log.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Test API Call (API Call)
    $(document).on('click', '.accuflow-sheets-test-api-call', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var apiEndpointUrl = button.closest('tr').find('.accuflow-api-endpoint-url').val();

        // Validate API endpoint URL contains placeholders
        if (apiEndpointUrl && (apiEndpointUrl.indexOf('{') === -1 || apiEndpointUrl.indexOf('}') === -1)) {
            alert('⚠️ Cảnh báo: API Endpoint URL có thể thiếu placeholders như {email}, {password}, {custom_link}, {note}.\n\nVí dụ đúng: http://103.195.237.31:3000/api/artlist?url={custom_link}&api_key=9e2aaf401f944e84bc7498c54e0a8f17');
        }

        var apiInputFields = [];
        button.closest('tr').find('input[name^="api_input_fields[' + productId + ']"]:checked').each(function() {
            apiInputFields.push($(this).val());
        });
        var apiCustomLinkRegex = button.closest('tr').find('input[name^="api_custom_link_regex[' + productId + ']"]').val();
        var resultSpan = $('#test-result-' + productId);

        resultSpan.html('<span class="spinner is-active"></span> Đang kiểm tra API Call...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_api_call_ajax',
                security: accuflow_ajax_object.test_api_call_nonce,
                product_id: productId,
                api_endpoint_url: apiEndpointUrl,
                api_input_fields: apiInputFields,
                api_custom_link_regex: apiCustomLinkRegex
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi kiểm tra API Call.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });


    // JSON converter
    $('#convert_json').click(function() {
        var jsonText = $('#json_converter').val().trim();

        if (!jsonText) {
            alert('Vui lòng paste nội dung JSON vào ô trên!');
            return;
        }

        try {
            var jsonData = JSON.parse(jsonText);

            if (jsonData.project_id) {
                $('#service_account_project_id').val(jsonData.project_id);
            }
            if (jsonData.private_key_id) {
                $('#service_account_private_key_id').val(jsonData.private_key_id);
            }
            if (jsonData.private_key) {
                var privateKey = jsonData.private_key.replace(/\\n/g, '\n');
                $('#service_account_private_key').val(privateKey);
            }
            if (jsonData.client_email) {
                $('#service_account_client_email').val(jsonData.client_email);
            }
            if (jsonData.client_id) {
                $('#service_account_client_id').val(jsonData.client_id);
            }
            if (jsonData.client_x509_cert_url) {
                $('#service_account_client_x509_cert_url').val(jsonData.client_x509_cert_url);
            }

            $('#json_converter').val(''); // Clear converter field
            alert('✅ Chuyển đổi thành công! Các trường đã được điền tự động. Hãy nhấn "Lưu Cài đặt" để lưu.');

            $('html, body').animate({
                scrollTop: $('#service_account_project_id').offset().top - 100
            }, 500);

        } catch (e) {
            alert('❌ Lỗi: JSON không hợp lệ. Vui lòng kiểm tra lại nội dung JSON.\n\nLỗi chi tiết: ' + e.message);
        }
    });

    // Clear fields
    $('#clear_fields').click(function() {
        if (confirm('Bạn có chắc muốn xóa tất cả các trường Service Account?')) {
            $('#service_account_project_id, #service_account_private_key_id, #service_account_private_key, #service_account_client_email, #service_account_client_id, #service_account_client_x509_cert_url, #json_converter').val('');
            alert('✅ Đã xóa tất cả các trường!');
        }
    });

    // Test connection button
    $('#test_connection_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang kiểm tra kết nối...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_connection_ajax',
                security: accuflow_ajax_object.test_connection_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultContent.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi kiểm tra kết nối.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Detailed diagnosis button
    $('#detailed_diagnosis_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang chạy chẩn đoán chi tiết...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_detailed_diagnosis_ajax',
                security: accuflow_ajax_object.detailed_diagnosis_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html(response.data);
                } else {
                    resultContent.html('<span class="test-error">Lỗi: ' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi chạy chẩn đoán.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Download CA Certificates button
    $('#download_cacert_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang tải xuống CA Certificates...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_download_cacert_ajax',
                security: accuflow_ajax_object.download_cacert_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    $resultContent.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi tải CA Certificates.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Close diagnosis box
    $(document).on('click', '.button-close-diagnosis', function() {
        $('#diagnosis_results_box').fadeOut();
    });


    // Product search and pagination
    $('#product-search-button').on('click', function() {
        var searchQuery = $('#product-search-input').val();
        loadProductList(searchQuery, 1);
    });

    $(document).on('click', '.accuflow-pagination-link', function(e) {
        e.preventDefault();
        var pageNum = $(this).data('page');
        var searchQuery = $('#product-search-input').val();
        loadProductList(searchQuery, pageNum);
    });

    // --- WC Email Management Scripts ---

    // Open Email Settings Modal - event listener
    $(document).on('click', '.accuflow-open-email-settings-modal', function() {
        console.log('Nút Cài đặt đã được nhấn!'); // Added console log

        var button = $(this);
        var emailKey = button.data('email-key');
        var $row = $('tr[data-email-key="' + emailKey + '"]');

        // Lấy dữ liệu hiện tại từ hidden inputs của hàng tương ứng trong bảng chính
        var emailLabel = $row.find('strong').text();
        // Cần đảm bảo các input hidden này có giá trị đúng.
        // Lỗi ReferenceError ban đầu khiến các val() này không chạy được.
        var currentSubject = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][subject]"]').val();
        var currentHeading = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][heading]"]').val();
        var currentTemplate = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][template]"]').val();

        // Lấy các giá trị cấu hình email chung (logo, màu sắc...) từ các giá trị đã được localize
        var globalSettings = accuflow_ajax_object.global_email_settings_js;

        // Điền dữ liệu vào các trường trong modal
        $('#accuflow-modal-email-label').text(emailLabel);
        $('#modal_email_subject').val(currentSubject);
        $('#modal_email_heading').val(currentHeading);
        $('#modal_email_template').val(currentTemplate);

        // Điền dữ liệu cho tab "Cài đặt Chung Email" trong modal
        $('#modal_email_header_image').val(globalSettings.email_header_image);
        $('#modal_email_header_image_width').val(globalSettings.email_header_image_width);
        $('#modal_email_header_alignment').val(globalSettings.email_header_alignment);
        $('#modal_email_font_family').val(globalSettings.email_font_family);
        // Kích hoạt Iris color picker và set màu
        $('#modal_email_base_color').val(globalSettings.email_base_color);
        $('#modal_email_background_color').val(globalSettings.email_background_color);
        $('#modal_email_body_background_color').val(globalSettings.email_body_background_color);
        $('#modal_email_text_color').val(globalSettings.email_text_color);
        $('#modal_email_footer_text_color').val(globalSettings.email_footer_text_color);
        // Sau khi set giá trị, cần gọi .iris('color', ...) để cập nhật màu hiển thị
        $('#modal_email_base_color').iris('color', globalSettings.email_base_color);
        $('#modal_email_background_color').iris('color', globalSettings.email_background_color);
        $('#modal_email_body_background_color').iris('color', globalSettings.email_body_background_color);
        $('#modal_email_text_color').iris('color', globalSettings.email_text_color);
        $('#modal_email_footer_text_color').iris('color', globalSettings.email_footer_text_color);


        $('#modal_email_footer_text').val(globalSettings.email_footer_text);
        
        // Đặt emailKey vào các nút Preview và Gửi thử trong modal
        $modal.find('.accuflow-preview-email-button').data('email-key', emailKey);
        $modal.find('.accuflow-send-test-email-button').data('email-key', emailKey);

        // Lưu emailKey vào modal để sử dụng sau khi lưu
        $modal.data('current-email-key', emailKey);

        // Reset modal tabs (mở tab chỉnh sửa template mặc định)
        $modal.find('.accuflow-modal-tab-button').removeClass('active');
        $modal.find('.accuflow-modal-tab-button[data-modal-tab="template-editor"]').addClass('active');
        $modal.find('.accuflow-modal-tab-content').removeClass('active').hide();
        $modal.find('#accuflow-modal-tab-template-editor').addClass('active').show();

        // Clear previous preview and test results
        $modal.find('.accuflow-preview-iframe').contents().find('html').empty();
        $modal.find('.accuflow-test-email-result').empty();
        $modal.find('.accuflow-email-preview-iframe-wrapper').hide(); // Hide iframe wrapper initially

        // Hiển thị modal
        $modal.fadeIn();
    });

    // Close Email Settings Modal
    $(document).on('click', '.accuflow-close-preview-modal', function() {
        $(this).closest('#accuflow-email-settings-modal').fadeOut();
    });

    // Save Individual Email Settings from Modal
    $(document).on('click', '.accuflow-save-modal-settings', function() {
        var emailKey = $modal.data('current-email-key');
        var $row = $('tr[data-email-key="' + emailKey + '"]');

        // Lấy dữ liệu từ modal
        var newSubject = $('#modal_email_subject').val();
        var newHeading = $('#modal_email_heading').val();
        var newTemplate = $('#modal_email_template').val();

        // Cập nhật giá trị vào hidden input fields trong row của bảng chính
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][subject]"]').val(newSubject);
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][heading]"]').val(newHeading);
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][template]"]').val(newTemplate);
        
        // Cập nhật hiển thị tiêu đề mặc định trong bảng (cột thứ 3)
        $row.find('td:eq(3) code').text(newSubject);

        $modal.fadeOut();
        // Hiển thị thông báo "Đã cập nhật (cần Lưu Cài đặt Email)"
        $('.accuflow-admin-page .notice').remove();
        var successNotice = '<div class="notice notice-warning is-dismissible"><p>Cài đặt email <strong>' + emailKey + '</strong> đã được cập nhật (cần nhấn nút "Lưu Cài đặt Email" để lưu hoàn toàn).</p></div>';
        $('.accuflow-admin-page h1').after(successNotice);
    });

    // Save Global Email Settings from Modal
    $(document).on('click', '.accuflow-save-modal-global-settings', function() {
        // Lấy dữ liệu từ các trường cài đặt chung trong modal
        var newHeaderImage = $('#modal_email_header_image').val();
        var newHeaderImageWidth = $('#modal_email_header_image_width').val();
        var newHeaderAlignment = $('#modal_email_header_alignment').val();
        var newFontFamily = $('#modal_email_font_family').val();
        var newBaseColor = $('#modal_email_base_color').val();
        var newBackgroundColor = $('#modal_email_background_color').val();
        var newBodyBackgroundColor = $('#modal_email_body_background_color').val();
        var newTextColor = $('#modal_email_text_color').val();
        var newFooterTextColor = $('#modal_email_footer_text_color').val();
        var newFooterText = $('#modal_email_footer_text').val();

        // Cập nhật giá trị vào các input ẩn của form chính (để khi submit sẽ gửi đi)
        $('[name="email_header_image"]').val(newHeaderImage);
        $('[name="email_header_image_width"]').val(newHeaderImageWidth);
        $('[name="email_header_alignment"]').val(newHeaderAlignment);
        $('[name="email_font_family"]').val(newFontFamily);
        $('[name="email_base_color"]').val(newBaseColor);
        $('[name="email_background_color"]').val(newBackgroundColor);
        $('[name="email_body_background_color"]').val(newBodyBackgroundColor);
        $('[name="email_text_color"]').val(newTextColor);
        $('[name="email_footer_text_color"]').val(newFooterTextColor);
        $('[name="email_footer_text"]').val(newFooterText);
        
        $modal.fadeOut();
        // Hiển thị thông báo "Đã cập nhật (cần Lưu Cài đặt Email)"
        $('.accuflow-admin-page .notice').remove();
        var successNotice = '<div class="notice notice-warning is-dismissible"><p>Cài đặt chung email đã được cập nhật (cần nhấn nút "Lưu Cài đặt Email" để lưu hoàn toàn).</p></div>';
        $('.accuflow-admin-page h1').after(successNotice);
    });


    // Handle recipient type select change (in main table)
    $(document).on('change', '.accuflow-recipient-type-select', function() {
        var $parentTd = $(this).closest('td');
        var selectedValue = $(this).val();
        if (selectedValue === 'custom_email_input') {
            $parentTd.find('.accuflow-custom-recipient-email-field').slideDown();
        } else {
            $parentTd.find('.accuflow-custom-recipient-email-field').slideUp();
        }
    });

    // Initial display for custom recipient email fields on load
    $('.accuflow-recipient-type-select').each(function() {
        if ($(this).val() === 'custom_email_input') {
            $(this).closest('td').find('.accuflow-custom-recipient-email-field').show();
        }
    });

    // Removed email functionality to avoid WooCommerce conflicts

    // Modal Tabs functionality
    $(document).on('click', '.accuflow-modal-tab-button', function() {
        var tabId = $(this).data('modal-tab');
        // $modal đã được khai báo ở đầu ready scope

        $modal.find('.accuflow-modal-tab-button').removeClass('active');
        $(this).addClass('active');

        $modal.find('.accuflow-modal-tab-content').removeClass('active').hide();
        $modal.find('#accuflow-modal-tab-' + tabId).addClass('active').show();

        // Initialize color pickers if on global settings tab
        if (tabId === 'global-settings') {
            setTimeout(function() { // Delay to ensure elements are rendered
                $('.accuflow-color-picker').each(function() {
                    if (!$(this).data('iris')) { // Only initialize if not already
                        $(this).iris({
                            hide: true,
                            palettes: true,
                            change: function(event, ui) {
                                $(this).css({ backgroundColor: ui.color.toString() });
                            }
                        });
                    }
                });
            }, 100);
        }
    });

    // API Settings Modal
    var currentProductId = null;
    var $modal = $('#accuflow-api-settings-modal');

    // Open modal when clicking API settings button
    $(document).on('click', '.accuflow-api-settings-btn', function() {
        currentProductId = $(this).data('product-id');
        var $row = $(this).closest('tr');

        // Load current values from hidden inputs
        var apiEndpoint = $row.find('input[name="api_endpoint_url[' + currentProductId + ']"]').val();
        var jsonFilter = $row.find('input[name="api_json_filter[' + currentProductId + ']"]').val();
        var noteFilter = $row.find('input[name="api_note_filter[' + currentProductId + ']"]').val();
        var customLinkRegex = $row.find('input[name="api_custom_link_regex[' + currentProductId + ']"]').val();
        var showFrontend = $row.find('input[name="api_show_frontend[' + currentProductId + ']"]').val();

        // Get input fields
        var inputFields = [];
        $row.find('input[name="api_input_fields[' + currentProductId + '][]"]').each(function() {
            inputFields.push($(this).val());
        });

        // Get placeholders
        var placeholders = {};
        $row.find('input[name^="api_placeholders[' + currentProductId + ']"]').each(function() {
            var name = $(this).attr('name');
            var match = name.match(/\[([^\]]+)\]$/);
            if (match) {
                placeholders[match[1]] = $(this).val();
            }
        });

        // Populate modal
        $('#modal-api-endpoint').val(apiEndpoint);
        $('#modal-json-filter').val(jsonFilter);
        $('#modal-note-filter').val(noteFilter);
        $('#modal-regex-custom_link').val(customLinkRegex);
        $('#modal-show-frontend').prop('checked', showFrontend === '1');

        // Set input field checkboxes
        $('.modal-input-field').prop('checked', false);
        inputFields.forEach(function(field) {
            $('.modal-input-field[value="' + field + '"]').prop('checked', true);
            $('.placeholder-field[data-field="' + field + '"]').show();
        });

        // Set placeholders
        Object.keys(placeholders).forEach(function(field) {
            $('#modal-placeholder-' + field).val(placeholders[field]);
        });

        $modal.show().addClass('show');
    });

    // Close modal
    $('.accuflow-modal-close, #accuflow-cancel-api-settings').on('click', function() {
        $modal.removeClass('show');
        setTimeout(function() {
            $modal.hide();
        }, 300);
    });

    // Close modal when clicking outside
    $(window).on('click', function(event) {
        if (event.target === $modal[0]) {
            $modal.removeClass('show');
            setTimeout(function() {
                $modal.hide();
            }, 300);
        }
    });

    // Toggle placeholder fields in modal
    $(document).on('change', '.modal-input-field', function() {
        var field = $(this).val();
        var $placeholderField = $('.placeholder-field[data-field="' + field + '"]');

        if ($(this).is(':checked')) {
            $placeholderField.slideDown(300).addClass('active');
        } else {
            $placeholderField.slideUp(300).removeClass('active');
        }
    });

    // Auto-generate regex pattern based on custom link placeholder
    $(document).on('input', '#modal-placeholder-custom_link', function() {
        var placeholder = $(this).val();
        var regex = '(.+)'; // Default regex

        // Auto-detect common patterns
        if (placeholder.includes('artlist.io')) {
            regex = 'https?://artlist\\.io/(.+)';
        } else if (placeholder.includes('youtube.com')) {
            regex = 'https?://(?:www\\.)?youtube\\.com/watch\\?v=([\\w-]+)';
        } else if (placeholder.includes('spotify.com')) {
            regex = 'https?://open\\.spotify\\.com/track/([\\w]+)';
        } else if (placeholder.match(/https?:\/\//)) {
            regex = 'https?://[^/]+/(.+)';
        }

        $('#modal-regex-custom_link').val(regex);
    });

    // Save API settings
    $('#accuflow-save-api-settings').on('click', function() {
        if (!currentProductId) return;

        var $row = $('tr').has('.accuflow-api-settings-btn[data-product-id="' + currentProductId + '"]');

        // Update hidden inputs
        $row.find('input[name="api_endpoint_url[' + currentProductId + ']"]').val($('#modal-api-endpoint').val());
        $row.find('input[name="api_json_filter[' + currentProductId + ']"]').val($('#modal-json-filter').val());
        $row.find('input[name="api_note_filter[' + currentProductId + ']"]').val($('#modal-note-filter').val());
        $row.find('input[name="api_custom_link_regex[' + currentProductId + ']"]').val($('#modal-regex-custom_link').val());
        $row.find('input[name="api_show_frontend[' + currentProductId + ']"]').val($('#modal-show-frontend').is(':checked') ? '1' : '');

        // Remove existing input field inputs
        $row.find('input[name="api_input_fields[' + currentProductId + '][]"]').remove();

        // Add new input field inputs
        $('.modal-input-field:checked').each(function() {
            $row.find('.accuflow-api-config').append('<input type="hidden" name="api_input_fields[' + currentProductId + '][]" value="' + $(this).val() + '">');
        });

        // Remove existing placeholder inputs
        $row.find('input[name^="api_placeholders[' + currentProductId + ']"]').remove();

        // Add new placeholder inputs
        $('.modal-input-field:checked').each(function() {
            var field = $(this).val();
            var placeholder = $('#modal-placeholder-' + field).val();
            if (placeholder) {
                $row.find('.accuflow-api-config').append('<input type="hidden" name="api_placeholders[' + currentProductId + '][' + field + ']" value="' + placeholder + '">');
            }
        });

        $modal.removeClass('show');
        setTimeout(function() {
            $modal.hide();
        }, 300);

        // Show success message
        alert('✅ Cấu hình API đã được lưu! Nhớ click "Lưu tất cả thay đổi" để lưu vào database.');
    });

    // Test API in modal
    $('#modal-test-api').on('click', function() {
        var apiUrl = $('#modal-api-endpoint').val();
        var jsonFilter = $('#modal-json-filter').val();
        var $resultDiv = $('#modal-test-result');
        var $button = $(this);

        if (!apiUrl) {
            $resultDiv.html('<span style="color: red;">⚠️ Vui lòng nhập API URL trước</span>');
            return;
        }

        $button.prop('disabled', true).text('🔄 Đang test...');
        $resultDiv.html('<span style="color: #666;">Đang gọi API...</span>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'accuflow_test_api_call_ajax',
                security: accuflow_ajax_object.test_api_call_nonce,
                product_id: currentProductId,
                api_endpoint_url: apiUrl,
                api_json_filter: jsonFilter
            },
            success: function(response) {
                if (response.success) {
                    $resultDiv.html('<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px; color: #155724;"><strong>✅ Thành công!</strong><br>' + response.data + '</div>');
                } else {
                    $resultDiv.html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 4px; color: #721c24;"><strong>❌ Lỗi:</strong><br>' + response.data + '</div>');
                }
            },
            error: function() {
                $resultDiv.html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 4px; color: #721c24;"><strong>❌ Lỗi kết nối</strong></div>');
            },
            complete: function() {
                $button.prop('disabled', false).text('🧪 Test API');
            }
        });
    });

    // Column Configuration is now always string method - no toggle needed

    // Dynamic Column Mapping Preview
    function updateColumnPreview() {
        var columnNames = $('#column_mapping_string').val().trim();
        var columnIndices = $('#column_indices_string').val().trim();
        var $preview = $('#string-config-preview');
        var $previewContent = $('#preview-content');

        if (columnNames === '' || columnIndices === '') {
            $preview.hide();
            return;
        }

        var names = columnNames.split('|').map(function(name) { return name.trim(); });
        var indices = columnIndices.split('|').map(function(index) { return parseInt(index.trim()); });
        var columnLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

        var previewHtml = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 8px;">';

        var maxLength = Math.max(names.length, indices.length);

        for (var i = 0; i < maxLength; i++) {
            var name = names[i] || '❌ Thiếu tên';
            var index = indices[i];
            var columnLetter = (index !== undefined && index >= 0) ? columnLetters[index] || '?' : '❌';

            var statusClass = '';
            if (!names[i] || index === undefined || index < 0) {
                statusClass = 'style="background: #ffebee; border-left-color: #f44336;"';
            }

            previewHtml += '<div class="preview-mapping" ' + statusClass + '>';
            previewHtml += '<span class="preview-column">Col ' + columnLetter + ' (' + (index !== undefined ? index : '?') + ')</span>';
            previewHtml += '<span class="preview-arrow">→</span>';
            previewHtml += '<span class="preview-field">' + name + '</span>';
            previewHtml += '</div>';
        }

        previewHtml += '</div>';

        if (names.length !== indices.length) {
            previewHtml += '<div style="color: #f44336; margin-top: 12px; padding: 8px; background: #ffebee; border-radius: 4px;">';
            previewHtml += '⚠️ Số lượng tên cột (' + names.length + ') và chỉ số cột (' + indices.length + ') không khớp!';
            previewHtml += '</div>';
        }

        $previewContent.html(previewHtml);
        $preview.show();
    }

    $(document).on('input', '#column_mapping_string, #column_indices_string', updateColumnPreview);

    // Initialize preview on page load (string config is always visible now)
    $(document).ready(function() {
        updateColumnPreview();
    });

    // Add Column Button
    $(document).on('click', '#add-column-btn', function() {
        var $columnNames = $('#column_mapping_string');
        var $columnIndices = $('#column_indices_string');

        var currentNames = $columnNames.val().trim();
        var currentIndices = $columnIndices.val().trim();

        var newColumnName = prompt('Nhập tên cột mới:', 'NewColumn');
        if (!newColumnName) return;

        // Calculate next index
        var indices = currentIndices ? currentIndices.split('|').map(function(i) { return parseInt(i.trim()); }) : [];
        var nextIndex = indices.length > 0 ? Math.max.apply(Math, indices) + 1 : 0;

        // Add to names
        var newNames = currentNames ? currentNames + '|' + newColumnName : newColumnName;
        $columnNames.val(newNames);

        // Add to indices
        var newIndices = currentIndices ? currentIndices + '|' + nextIndex : nextIndex.toString();
        $columnIndices.val(newIndices);

        updateColumnPreview();
    });

    // Reset Default Button
    $(document).on('click', '#reset-default-btn', function() {
        if (confirm('Bạn có chắc muốn reset về cấu hình mặc định?')) {
            $('#column_mapping_string').val('ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute');
            $('#column_indices_string').val('0|1|2|3|4|5|6|7|8|9|10|11|12|13');
            updateColumnPreview();
        }
    });

    // Basic email validation
    function isValidEmail(email) {
        var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        return regex.test(email);
    }

    // Validate API endpoint URLs before form submission
    $('form').on('submit', function(e) {
        var hasApiEndpointError = false;

        $(this).find('input[name*="api_endpoint_url"]').each(function() {
            var url = $(this).val();
            if (url && (url.indexOf('{') === -1 || url.indexOf('}') === -1)) {
                hasApiEndpointError = true;
                $(this).css('border', '2px solid red');

                // Show warning message
                var warningMsg = $(this).siblings('.api-url-warning');
                if (warningMsg.length === 0) {
                    $(this).after('<div class="api-url-warning" style="color: red; font-size: 12px; margin-top: 5px;">⚠️ URL có thể thiếu placeholders như {custom_link}</div>');
                }
            } else {
                $(this).css('border', '');
                $(this).siblings('.api-url-warning').remove();
            }
        });

        if (hasApiEndpointError) {
            if (!confirm('⚠️ Một số API Endpoint URL có thể thiếu placeholders.\n\nBạn có chắc chắn muốn lưu không?')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
