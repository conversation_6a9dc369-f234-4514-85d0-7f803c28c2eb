<?php
/**
 * AccuFlow - Google Sheets Integration: Logic xử lý phân phối tài k<PERSON>n
 *
 * @package AccuFlow
 * @subpackage Includes
 * @since 2.5.1
 */

if (!defined('ABSPATH')) {
    exit;
}

if (!class_exists('AccuFlow_Order_Fulfillment')) {
    class AccuFlow_Order_Fulfillment
    {
        private $config;
        private $google_api;
        // Removed email template to avoid WooCommerce conflicts

        public function __construct(AccuFlow_Config $config, AccuFlow_Google_API $google_api)
        {
            $this->config         = $config;
            $this->google_api     = $google_api;
            // Removed email template dependency
        }

        public function init()
        {
            add_action('woocommerce_order_status_completed', [$this, 'distribute_account_on_order_complete'], 10, 1);
            add_action('woocommerce_before_add_to_cart_button', [$this, 'display_api_input_fields'], 10);
            add_filter('woocommerce_add_to_cart_validation', [$this, 'validate_api_input_fields'], 10, 3);
            add_filter('woocommerce_add_cart_item_data', [$this, 'save_api_input_fields_to_cart_item'], 10, 3);
            add_action('woocommerce_checkout_create_order_line_item', [$this, 'save_api_input_fields_to_order_item'], 10, 4);

            // Removed WooCommerce email management to avoid conflicts

            // API endpoint
            add_action('rest_api_init', function () {
                register_rest_route('accuflow/v1', '/note-data', [
                    'methods' => 'GET',
                    'callback' => [$this, 'get_order_note_data'],
                    'permission_callback' => '__return_true',
                ]);
            });
        }

        public function display_api_input_fields()
        {
            global $product;
            if (!$product) return;

            $config_data = $this->config->get_config();
            $product_id_to_check = $product->get_id();

            $fulfillment_method = get_post_meta($product_id_to_check, $config_data['product_fulfillment_method_meta_key'], true);
            if ($product->is_type('variation') && !$fulfillment_method) {
                $product_id_to_check = $product->get_parent_id();
                $fulfillment_method = get_post_meta($product_id_to_check, $config_data['product_fulfillment_method_meta_key'], true);
            }

            if ($fulfillment_method !== 'api_call') return;

            $api_input_fields = get_post_meta($product_id_to_check, $config_data['product_api_input_fields_meta_key'], true);
            if (empty($api_input_fields) || !is_array($api_input_fields)) return;

            // Get custom placeholders
            $custom_placeholders = get_post_meta($product_id_to_check, $config_data['product_api_placeholders_meta_key'], true);
            if (!is_array($custom_placeholders)) {
                $custom_placeholders = [
                    'email' => 'Nhập email của bạn',
                    'password' => 'Nhập password',
                    'custom_link' => 'https://example.com/your-link',
                    'note' => 'Ghi chú thêm (tùy chọn)'
                ];
            }

            echo '<div class="accuflow-api-inputs" style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px;">';
            echo '<h4 style="margin: 0 0 15px 0; color: #495057;">📋 Thông tin bổ sung</h4>';

            if (in_array('email', $api_input_fields)) {
                $placeholder = $custom_placeholders['email'] ?? 'Nhập email của bạn';
                echo '<p class="form-row form-row-wide"><label for="accuflow_user_email_input">Email <abbr class="required" title="required">*</abbr></label><input type="email" class="input-text" name="accuflow_user_email_input" id="accuflow_user_email_input" placeholder="' . esc_attr($placeholder) . '"></p>';
            }
            if (in_array('password', $api_input_fields)) {
                $placeholder = $custom_placeholders['password'] ?? 'Nhập password';
                echo '<p class="form-row form-row-wide"><label for="accuflow_user_password_input">Mật khẩu <abbr class="required" title="required">*</abbr></label><input type="password" class="input-text" name="accuflow_user_password_input" id="accuflow_user_password_input" placeholder="' . esc_attr($placeholder) . '"></p>';
            }
            if (in_array('custom_link', $api_input_fields)) {
                $placeholder = $custom_placeholders['custom_link'] ?? 'https://example.com/your-link';
                echo '<p class="form-row form-row-wide"><label for="accuflow_user_custom_link_input">Link tùy chỉnh <abbr class="required" title="required">*</abbr></label><input type="url" class="input-text" name="accuflow_user_custom_link_input" id="accuflow_user_custom_link_input" placeholder="' . esc_attr($placeholder) . '"></p>';
            }
            if (in_array('note', $api_input_fields)) {
                $placeholder = $custom_placeholders['note'] ?? 'Ghi chú thêm (tùy chọn)';
                echo '<p class="form-row form-row-wide"><label for="accuflow_user_note_input">Ghi chú</label><textarea class="input-text" name="accuflow_user_note_input" id="accuflow_user_note_input" placeholder="' . esc_attr($placeholder) . '"></textarea></p>';
            }
            echo '</div>';
        }
        public function validate_api_input_fields($passed, $product_id, $quantity)
        {
            $product = wc_get_product($product_id);
            $config_data = $this->config->get_config();
            $product_id_to_check = $product->is_type('variation') ? $product->get_parent_id() : $product_id;

            $fulfillment_method = get_post_meta($product_id_to_check, $config_data['product_fulfillment_method_meta_key'], true);
            if ($fulfillment_method !== 'api_call') return $passed;

            $api_input_fields = get_post_meta($product_id_to_check, $config_data['product_api_input_fields_meta_key'], true);
            if (empty($api_input_fields)) return $passed;

            if (in_array('email', $api_input_fields) && empty($_POST['accuflow_user_email_input'])) {
                wc_add_notice('Vui lòng nhập Email.', 'error');
                $passed = false;
            }
            if (in_array('password', $api_input_fields) && empty($_POST['accuflow_user_password_input'])) {
                wc_add_notice('Vui lòng nhập Mật khẩu.', 'error');
                $passed = false;
            }
            if (in_array('custom_link', $api_input_fields)) {
                if (empty($_POST['accuflow_user_custom_link_input'])) {
                    wc_add_notice('Vui lòng nhập Link tùy chỉnh.', 'error');
                    $passed = false;
                } else {
                    $user_link = $_POST['accuflow_user_custom_link_input'];
                    $regex = get_post_meta($product_id_to_check, $config_data['product_api_custom_link_regex_meta_key'], true);

                    // Debug logging
                    error_log('AccuFlow Debug: Validating custom link');
                    error_log('AccuFlow Debug: User input: ' . $user_link);
                    error_log('AccuFlow Debug: Regex pattern: ' . $regex);

                    if (!empty($regex)) {
                        // Escape regex delimiters if they exist in the pattern
                        $escaped_regex = str_replace('/', '\/', $regex);
                        $validation_result = preg_match('/' . $escaped_regex . '/', $user_link);

                        error_log('AccuFlow Debug: Escaped regex: ' . $escaped_regex);
                        error_log('AccuFlow Debug: Validation result: ' . ($validation_result ? 'PASS' : 'FAIL'));

                        if (!$validation_result) {
                            wc_add_notice('Định dạng Link tùy chỉnh không hợp lệ. Vui lòng kiểm tra lại format.', 'error');
                            $passed = false;
                        }
                    }
                }
            }

            return $passed;
        }

        public function save_api_input_fields_to_cart_item($cart_item_data, $product_id, $variation_id)
        {
            if (isset($_POST['accuflow_user_email_input'])) {
                $cart_item_data['accuflow_user_email_input'] = sanitize_email($_POST['accuflow_user_email_input']);
            }
            if (isset($_POST['accuflow_user_password_input'])) {
                $cart_item_data['accuflow_user_password_input'] = sanitize_text_field($_POST['accuflow_user_password_input']);
            }
            if (isset($_POST['accuflow_user_custom_link_input'])) {
                $cart_item_data['accuflow_user_custom_link_input'] = esc_url_raw($_POST['accuflow_user_custom_link_input']);
            }
            if (isset($_POST['accuflow_user_note_input'])) {
                $cart_item_data['accuflow_user_note_input'] = sanitize_text_field($_POST['accuflow_user_note_input']);
            }

            return $cart_item_data;
        }

        public function save_api_input_fields_to_order_item($item, $cart_item_key, $values, $order)
        {
            if (isset($values['accuflow_user_email_input'])) {
                $item->add_meta_data('_accuflow_user_email_input', $values['accuflow_user_email_input']);
            }
            if (isset($values['accuflow_user_password_input'])) {
                $item->add_meta_data('_accuflow_user_password_input', $values['accuflow_user_password_input']);
            }
            if (isset($values['accuflow_user_custom_link_input'])) {
                $item->add_meta_data('_accuflow_user_custom_link_input', $values['accuflow_user_custom_link_input']);
            }
            if (isset($values['accuflow_user_note_input'])) {
                $item->add_meta_data('_accuflow_user_note_input', $values['accuflow_user_note_input']);
            }
        }

        public function get_order_note_data($request)
        {
            $order_id = intval($request['order_id'] ?? 0);
            if (!$order_id || !($order = wc_get_order($order_id))) {
                return new WP_REST_Response(['error' => 'Invalid order'], 400);
            }

            $notes = [];
            foreach ($order->get_items() as $item) {
                $note = $item->get_meta('_accuflow_user_note_input', true);
                if ($note) {
                    $notes[] = $note;
                }
            }

            return new WP_REST_Response(['notes' => $notes], 200);
        }
        public function distribute_account_on_order_complete($order_id)
        {
            error_log('AccuFlow Debug: distribute_account_on_order_complete triggered for order #' . $order_id);

            if (!class_exists('WooCommerce')) {
                error_log('AccuFlow Debug: WooCommerce not found');
                return;
            }

            $order = wc_get_order($order_id);
            if (!$order) {
                error_log('AccuFlow Debug: Order not found for ID: ' . $order_id);
                return;
            }

            error_log('AccuFlow Debug: Processing order #' . $order_id . ' with status: ' . $order->get_status());

            $config_data = $this->config->get_config();
            $customer_email = $order->get_billing_email();

            foreach ($order->get_items() as $item_id => $item) {
                $product = $item->get_product();
                if (!$product) continue;

                $product_in_order_name = $product->get_name();
                $product_id_for_meta = $product->get_id();
                $parent_product_id = $product->is_type('variation') ? $product->get_parent_id() : null;

                error_log('AccuFlow Debug: Processing product: ' . $product_in_order_name . ' (ID: ' . $product_id_for_meta . ')');

                $fulfillment_method = get_post_meta($product_id_for_meta, $config_data['product_fulfillment_method_meta_key'], true);
                error_log('AccuFlow Debug: Fulfillment method for product ID ' . $product_id_for_meta . ': ' . ($fulfillment_method ?: 'EMPTY'));
                if (empty($fulfillment_method) || $fulfillment_method === 'none') {
                    if ($parent_product_id) {
                        $fulfillment_method = get_post_meta($parent_product_id, $config_data['product_fulfillment_method_meta_key'], true);
                        if ($fulfillment_method && $fulfillment_method !== 'none') {
                            $product_id_for_meta = $parent_product_id;
                        }
                    }
                }

                if (empty($fulfillment_method) || $fulfillment_method === 'none') {
                    continue;
                }

                $email_subject = sprintf('Thông tin tài khoản của bạn từ %s - Đơn hàng #%s', $config_data['shop_name'], $order_id);
                $email_data = [
                    'order_id'        => $order_id,
                    'username'        => 'Error',
                    'password'        => 'Error',
                    'login_url'       => $config_data['support_url'],
                    'special_message' => 'Rất xin lỗi về sự cố đáng tiếc này!! Vui lòng liên hệ Fanpage để được hỗ trợ sớm nhất!! Chân Thành Xin Lỗi Quý Khách!!',
                    'call_to_action'  => 'liên hệ Fanpage',
                    'button_text'     => 'Liên hệ Fanpage',
                ];
                $customer_order_note_content = '';
                $admin_note_content = '';

                $user_note_filter = sanitize_text_field($item->get_meta('_accuflow_user_note_input', true));

                if ($fulfillment_method === 'google_sheet') {
                    // Enhanced logging for Google Sheets fulfillment
                    error_log('AccuFlow Debug: ===== GOOGLE SHEETS FULFILLMENT METHOD =====');
                    error_log('AccuFlow Debug: Product: ' . $product_in_order_name . ' (ID: ' . $product_id_for_meta . ')');
                    error_log('AccuFlow Debug: Order: #' . $order->get_id() . ' - Customer: ' . $order->get_billing_email());

                    $variation_attribute_value = null;
                    if ($product->is_type('variation') && $config_data['enable_variation_matching']) {
                        $variation_attribute_value = wc_get_formatted_variation($product, true, false, true);
                        error_log('AccuFlow Debug: Variation attribute value: ' . $variation_attribute_value);
                    }

                    // Get quantity from order item
                    $quantity = $item->get_quantity();
                    error_log('AccuFlow Debug: Processing quantity: ' . $quantity . ' for product: ' . $product_in_order_name);

                    // Log user input data for Google Sheets (if any)
                    $user_note_filter = sanitize_text_field($item->get_meta('_accuflow_user_note_input', true));
                    $user_input_data_gs = [
                        'email'       => $item->get_meta('_accuflow_user_email_input', true),
                        'password'    => $item->get_meta('_accuflow_user_password_input', true),
                        'custom_link' => $item->get_meta('_accuflow_user_custom_link_input', true),
                        'note'        => $user_note_filter,
                    ];
                    error_log('AccuFlow Debug: User input data for Google Sheets: ' . print_r($user_input_data_gs, true));

                    $sheet_gid_from_product = get_post_meta($product_id_for_meta, $config_data['product_sheet_tab_meta_key'], true);

                    if (empty($sheet_gid_from_product)) {
                        error_log('AccuFlow Debug: Product #' . $product_id_for_meta . ' (' . $product_in_order_name . ') has no GID Tab configured');
                        $admin_note_content = sprintf('Lỗi: Sản phẩm #%s (%s) không có GID Tab. Email lỗi đã được gửi.', $product_id_for_meta, $product_in_order_name);
                    } else {
                        error_log('AccuFlow Debug: Product #' . $product_id_for_meta . ' using GID: ' . $sheet_gid_from_product);
                        // Find multiple accounts based on quantity
                        $account_details_list = $this->google_api->find_multiple_available_accounts(
                            $sheet_gid_from_product,
                            $quantity,
                            $variation_attribute_value,
                            $user_note_filter
                        );

                        if (!empty($account_details_list)) {
                            $successful_accounts = [];
                            $failed_accounts = [];

                            // Update status for each account
                            foreach ($account_details_list as $account_details) {
                                $updated = $this->google_api->update_account_status($sheet_gid_from_product, $account_details['row_number'], $order_id, $order);
                                if ($updated) {
                                    $successful_accounts[] = $account_details;
                                } else {
                                    $failed_accounts[] = $account_details;
                                }
                            }

                            if (!empty($successful_accounts)) {
                                // Prepare email data with multiple accounts
                                $email_data['accounts'] = $successful_accounts;
                                $email_data['quantity'] = count($successful_accounts);
                                $email_data['special_message'] = 'Để bảo mật, chúng tôi khuyến khích bạn đổi mật khẩu ngay sau khi đăng nhập lần đầu.';
                                $email_data['call_to_action'] = 'đăng nhập vào tài khoản của mình';
                                $email_data['button_text'] = 'Đăng nhập ngay';

                                // For backward compatibility, set first account as main
                                $first_account = $successful_accounts[0];
                                $email_data['username'] = $first_account['username'];
                                $email_data['password'] = $first_account['password'];
                                $email_data['login_url'] = !empty($first_account['login_url']) ? $first_account['login_url'] : $config_data['support_url'];

                                // Create admin note
                                $account_ids = array_map(function($acc) { return $acc['id']; }, $successful_accounts);
                                $admin_note_content = sprintf('Thành công: %d tài khoản (IDs: %s) đã được phân phối cho đơn hàng #%s (%s).',
                                    count($successful_accounts),
                                    implode(', ', $account_ids),
                                    $order_id,
                                    $product_in_order_name
                                );

                                // Create customer note with all accounts
                                $customer_accounts_text = '';
                                foreach ($successful_accounts as $index => $account) {
                                    $customer_accounts_text .= sprintf("Tài khoản %d:\nTên đăng nhập: %s\nMật khẩu: %s\nLink đăng nhập: %s\n\n",
                                        $index + 1,
                                        $account['username'],
                                        $account['password'],
                                        !empty($account['login_url']) ? $account['login_url'] : $config_data['support_url']
                                    );
                                }
                                $customer_order_note_content = sprintf("Thông tin tài khoản của bạn cho sản phẩm \"%s\" (Số lượng: %d):\n\n%s",
                                    $product_in_order_name,
                                    count($successful_accounts),
                                    $customer_accounts_text
                                );

                                // Add warning if not all accounts were processed
                                if (count($successful_accounts) < $quantity) {
                                    $admin_note_content .= sprintf(' Cảnh báo: Chỉ có thể phân phối %d/%d tài khoản.', count($successful_accounts), $quantity);
                                }
                            } else {
                                $admin_note_content = sprintf('Lỗi: Không thể cập nhật trạng thái cho bất kỳ tài khoản nào trong Google Sheet cho đơn hàng #%s.', $order_id);
                            }
                        } else {
                            error_log('AccuFlow Debug: No available accounts found for product: ' . $product_in_order_name . ', quantity needed: ' . $quantity . ', GID: ' . $sheet_gid_from_product);
                            $admin_note_content = sprintf('Lỗi: Không tìm thấy đủ tài khoản "Available" cho sản phẩm %s (cần %d tài khoản).', $product_in_order_name, $quantity);
                        }
                    }
                } elseif ($fulfillment_method === 'api_call') {
                    $api_endpoint_url = get_post_meta($product_id_for_meta, $config_data['product_api_endpoint_meta_key'], true);
                    $api_json_filter = get_post_meta($product_id_for_meta, $config_data['product_api_json_filter_meta_key'], true);
                    
                    if (empty($api_endpoint_url)) {
                        $admin_note_content = "Lỗi: Sản phẩm được cấu hình API Call nhưng thiếu API Endpoint URL.";
                    } else {
                        $user_input_data = [
                            'email'       => $item->get_meta('_accuflow_user_email_input', true),
                            'password'    => $item->get_meta('_accuflow_user_password_input', true),
                            'custom_link' => $item->get_meta('_accuflow_user_custom_link_input', true),
                            'note'        => $user_note_filter,
                        ];

                        // Enhanced logging for API fulfillment
                        error_log('AccuFlow Debug: ===== API FULFILLMENT METHOD =====');
                        error_log('AccuFlow Debug: Product: ' . $product->get_name() . ' (ID: ' . $product_id_for_meta . ')');
                        error_log('AccuFlow Debug: Order: #' . $order->get_id() . ' - Customer: ' . $order->get_billing_email());
                        error_log('AccuFlow Debug: User input data collected: ' . print_r($user_input_data, true));
                        error_log('AccuFlow Debug: API Endpoint URL: ' . $api_endpoint_url);

                        // Create user input summary for notes and telegram
                        $user_input_summary = $this->format_user_input_summary($user_input_data, $product_in_order_name, $order_id);

                        $final_api_url = $api_endpoint_url;
                        foreach ($user_input_data as $key => $value) {
                            $final_api_url = str_replace('{' . $key . '}', urlencode($value), $final_api_url);
                        }

                        error_log('AccuFlow Debug: ===== API URL CONSTRUCTION =====');
                        error_log('AccuFlow Debug: Original API URL Template: ' . $api_endpoint_url);
                        error_log('AccuFlow Debug: User input data for replacement: ' . print_r($user_input_data, true));
                        error_log('AccuFlow Debug: Final constructed API URL: ' . $final_api_url);
                        error_log('AccuFlow Debug: ===== CALLING API =====');

                        $response = wp_remote_get($final_api_url, array('timeout' => 45, 'sslverify' => !$config_data['disable_ssl_verify']));

                        if (is_wp_error($response)) {
                            $error_message = $response->get_error_message();
                            error_log('AccuFlow Debug: API Call Error: ' . $error_message);
                            $admin_note_content = "❌ Lỗi API Call: {$error_message}\n\n" . $user_input_summary;

                            // Set error email data
                            $email_data['username'] = 'Error';
                            $email_data['password'] = 'N/A';
                            $email_data['special_message'] = 'Có lỗi kết nối API. Vui lòng liên hệ hỗ trợ.';
                        } else {
                            $body = wp_remote_retrieve_body($response);
                            $http_code = wp_remote_retrieve_response_code($response);
                            error_log('AccuFlow Debug: ===== API RESPONSE RECEIVED =====');
                            error_log('AccuFlow Debug: HTTP Status Code: ' . $http_code);
                            error_log('AccuFlow Debug: Response Body (first 500 chars): ' . substr($body, 0, 500));
                            error_log('AccuFlow Debug: Full Response Body Length: ' . strlen($body) . ' characters');

                            $api_response_data = json_decode($body, true);

                            if (json_last_error() !== JSON_ERROR_NONE) {
                                error_log('AccuFlow Debug: JSON Decode Error: ' . json_last_error_msg());
                                $admin_note_content = "❌ Lỗi JSON Response: " . json_last_error_msg() . "\n\n" . $user_input_summary;
                                $email_data['username'] = 'Error';
                                $email_data['password'] = 'N/A';
                                $email_data['special_message'] = 'API trả về dữ liệu không hợp lệ.';
                            }

                            if ($http_code == 200 && $api_response_data) {
                                error_log('AccuFlow Debug: API response successful, data: ' . json_encode($api_response_data));

                                // Lọc JSON response nếu có filter
                                $api_json_filter = get_post_meta($product_id_for_meta, $config_data['product_api_json_filter_meta_key'], true);
                                error_log('AccuFlow Debug: Using JSON filter: ' . $api_json_filter);

                                $filtered_result = $this->filter_json_response($api_response_data, $api_json_filter);
                                error_log('AccuFlow Debug: Filtered result: ' . print_r($filtered_result, true));

                                // Lọc riêng cho note nếu có note filter
                                $api_note_filter = get_post_meta($product_id_for_meta, $config_data['product_api_note_filter_meta_key'], true);
                                $note_filtered_result = null;
                                if (!empty($api_note_filter)) {
                                    $note_filtered_result = $this->filter_json_response($api_response_data, $api_note_filter);
                                }

                                if ($filtered_result !== null) {
                                    error_log('AccuFlow Debug: Processing filtered result, type: ' . gettype($filtered_result));

                                    if (is_array($filtered_result)) {
                                        // Multiple filters result (array)
                                        $admin_note_content = $this->format_admin_note_result($filtered_result, $product_in_order_name, $order_id);
                                        $customer_note_data = $note_filtered_result ?? $filtered_result;
                                        $customer_order_note_content = $this->format_customer_note_result($customer_note_data, $product_in_order_name);
                                    } else {
                                        // Template format result (string) or single result
                                        $display_result = (string) $filtered_result;
                                        $admin_note_content = "✅ API thành công cho đơn hàng #{$order_id} ({$product_in_order_name}):\n\n{$display_result}\n\n" . $user_input_summary;

                                        // Sử dụng note filter riêng cho customer note nếu có
                                        $customer_note_data = $note_filtered_result ?? $display_result;
                                        $customer_order_note_content = "🎯 Kết quả cho sản phẩm \"{$product_in_order_name}\":\n\n📋 Thông tin:\n{$customer_note_data}\n\n⏰ Cập nhật lúc: " . current_time('H:i \\n\\g\\à\\y d/m/Y');
                                    }

                                    // Set email data
                                    $email_data['username'] = 'Xem chi tiết';
                                    $email_data['password'] = 'Trong ghi chú đơn hàng';
                                    $email_data['special_message'] = 'Thông tin tài khoản đã được xử lý thành công. Vui lòng kiểm tra ghi chú đơn hàng để xem chi tiết đầy đủ.';

                                    error_log('AccuFlow Debug: Admin note content: ' . $admin_note_content);
                                    error_log('AccuFlow Debug: Customer note content: ' . $customer_order_note_content);
                                } else {
                                    error_log('AccuFlow Debug: Filtered result is null');
                                    $admin_note_content = "❌ Lỗi: Không thể lọc dữ liệu API";
                                    $email_data['username'] = 'Error';
                                    $email_data['password'] = 'N/A';
                                    $email_data['special_message'] = 'Có lỗi xảy ra khi xử lý dữ liệu API.';
                                }
                                
                                $email_data['login_url'] = $config_data['support_url'];
                                $email_data['call_to_action'] = 'xem chi tiết trong đơn hàng';
                                $email_data['button_text'] = 'Xem đơn hàng';
                            } else {
                                error_log('AccuFlow Debug: API error - HTTP Code: ' . $http_code . ', Body: ' . $body);
                                $admin_note_content = sprintf("❌ Lỗi API (HTTP %s): %s\n\n%s", $http_code, substr($body, 0, 200), $user_input_summary);

                                // Set error email data
                                $email_data['username'] = 'Error';
                                $email_data['password'] = 'N/A';
                                $email_data['special_message'] = 'Có lỗi xảy ra khi xử lý API. Vui lòng liên hệ hỗ trợ.';
                            }
                        }
                    }
                }

                if (!empty($admin_note_content)) {
                    error_log('AccuFlow Debug: Adding notes to order. Admin note length: ' . strlen($admin_note_content));

                    if ($email_data['username'] === 'Error') {
                        $customer_order_note_content = sprintf('Rất tiếc, có lỗi xảy ra khi phân phối tài khoản cho sản phẩm "%s". Vui lòng liên hệ %s để được hỗ trợ.', $product_in_order_name, $config_data['support_text']);
                        error_log('AccuFlow Debug: Error case - setting error customer note');
                    }

                    // Removed email sending to avoid WooCommerce conflicts
                    error_log('AccuFlow Debug: Email functionality removed to avoid WooCommerce conflicts');

                    error_log('AccuFlow Debug: Adding customer note (visible): ' . substr($customer_order_note_content, 0, 100) . '...');
                    error_log('AccuFlow Debug: Adding admin note (private): ' . substr($admin_note_content, 0, 100) . '...');

                    $order->add_order_note($customer_order_note_content, true);
                    $order->add_order_note($admin_note_content, false);
                    $order->save();

                    error_log('AccuFlow Debug: Order notes added and saved successfully');

                    // Send Telegram notification for debugging
                    $telegram_message = "🎯 <b>AccuFlow Debug - Đơn hàng #{$order_id}</b>\n\n";
                    $telegram_message .= "📦 <b>Sản phẩm:</b> {$product_in_order_name}\n";
                    $telegram_message .= "👤 <b>Email KH:</b> {$customer_email}\n\n";

                    // User input data
                    $telegram_message .= "📋 <b>THÔNG TIN KHÁCH HÀNG NHẬP:</b>\n";
                    $telegram_message .= "📧 Email: <code>" . ($user_input_data['email'] ?: '[Không có]') . "</code>\n";
                    $telegram_message .= "🔐 Password: <code>" . ($user_input_data['password'] ?: '[Không có]') . "</code>\n";
                    $telegram_message .= "🔗 Custom Link: <code>" . ($user_input_data['custom_link'] ?: '[Không có]') . "</code>\n";
                    $telegram_message .= "📝 Note: <code>" . ($user_input_data['note'] ?: '[Không có]') . "</code>\n\n";

                    // API URLs
                    $telegram_message .= "🔗 <b>API URL gốc:</b>\n<code>{$api_endpoint_url}</code>\n\n";
                    if (isset($final_api_url)) {
                        $telegram_message .= "🎯 <b>API URL cuối:</b>\n<code>{$final_api_url}</code>\n\n";
                    }

                    // Result
                    $telegram_message .= "✅ <b>Kết quả:</b>\n<code>" . substr($admin_note_content, 0, 300) . "</code>\n\n";
                    $telegram_message .= "⏰ <b>Thời gian:</b> " . current_time('H:i:s d/m/Y');

                    $this->send_telegram_debug($telegram_message);
                } else {
                    error_log('AccuFlow Debug: No admin note content to add');

                    // Even if no API call, still log user input for manual processing
                    if (isset($user_input_summary)) {
                        $fallback_note = "📋 THÔNG TIN KHÁCH HÀNG (Không có API call):\n\n" . $user_input_summary;
                        $order->add_order_note($fallback_note, false);
                        $order->save();

                        // Send to Telegram anyway
                        $telegram_message = "⚠️ <b>AccuFlow - Đơn hàng #{$order_id} (Không có API)</b>\n\n";
                        $telegram_message .= "📦 <b>Sản phẩm:</b> {$product_in_order_name}\n";
                        $telegram_message .= "👤 <b>Email KH:</b> {$customer_email}\n\n";
                        $telegram_message .= "📋 <b>THÔNG TIN KHÁCH HÀNG NHẬP:</b>\n";
                        if (isset($user_input_data)) {
                            $telegram_message .= "📧 Email: <code>" . ($user_input_data['email'] ?: '[Không có]') . "</code>\n";
                            $telegram_message .= "🔐 Password: <code>" . ($user_input_data['password'] ?: '[Không có]') . "</code>\n";
                            $telegram_message .= "🔗 Custom Link: <code>" . ($user_input_data['custom_link'] ?: '[Không có]') . "</code>\n";
                            $telegram_message .= "📝 Note: <code>" . ($user_input_data['note'] ?: '[Không có]') . "</code>\n\n";
                        }
                        $telegram_message .= "⏰ <b>Thời gian:</b> " . current_time('H:i:s d/m/Y');

                        $this->send_telegram_debug($telegram_message);
                    }
                }
            }
        }

        // Removed WooCommerce email management to avoid conflicts

        /**
         * Public method for testing JSON filter (used by admin)
         */
        public function test_filter_json_response($json_data, $json_filter) {
            return $this->filter_json_response($json_data, $json_filter);
        }

        /**
         * Lọc JSON response theo Multiple JSONPath hoặc Template Format
         */
        private function filter_json_response($json_data, $json_filter) {
            error_log('AccuFlow Debug: filter_json_response called with filter: ' . ($json_filter ?? 'NULL'));

            if (empty($json_filter) || !is_array($json_data)) {
                error_log('AccuFlow Debug: Empty filter or invalid JSON data');
                return $json_data;
            }

            // Trim whitespace from filter
            $json_filter = trim($json_filter);

            // Check if it's template format (contains {})
            if (strpos($json_filter, '{') !== false && strpos($json_filter, '}') !== false) {
                error_log('AccuFlow Debug: Using template format');
                return $this->process_template_format($json_data, $json_filter);
            }

            // Check if it's multiple filters (contains :)
            if (strpos($json_filter, ':') !== false) {
                error_log('AccuFlow Debug: Using multiple filters');
                return $this->process_multiple_json_filters($json_data, $json_filter);
            }

            // Single filter (backward compatibility)
            error_log('AccuFlow Debug: Using single filter');
            return $this->process_single_json_filter($json_data, $json_filter);
        }

        /**
         * Xử lý template format với placeholders
         * Ví dụ: "Link tải xuống: {$.url}\nTên tài khoản: {$.metadata.artistName}"
         */
        private function process_template_format($json_data, $template) {
            if (empty($template)) {
                return '';
            }

            error_log('AccuFlow Debug: Processing template format: ' . $template);

            // Find all placeholders in format {$.path}
            preg_match_all('/\{([^}]+)\}/', $template, $matches);

            if (empty($matches[1])) {
                error_log('AccuFlow Debug: No placeholders found in template');
                return $template; // No placeholders found
            }

            error_log('AccuFlow Debug: Found placeholders: ' . print_r($matches[1], true));

            $result = $template;

            foreach ($matches[1] as $placeholder) {
                $jsonpath = trim($placeholder);

                // Extract value using JSONPath
                $value = $this->process_single_json_filter($json_data, $jsonpath);
                error_log('AccuFlow Debug: JSONPath "' . $jsonpath . '" returned: ' . print_r($value, true));

                // Convert value to string
                if (is_array($value) || is_object($value)) {
                    $value_str = json_encode($value, JSON_UNESCAPED_UNICODE);
                } else {
                    $value_str = (string) $value;
                }

                // Replace placeholder with actual value
                $result = str_replace('{' . $placeholder . '}', $value_str, $result);
            }

            error_log('AccuFlow Debug: Final template result: ' . $result);
            return $result;
        }

        /**
         * Xử lý multiple JSON filters
         */
        private function process_multiple_json_filters($json_data, $filters_text) {
            $lines = explode("\n", trim($filters_text));
            $results = [];
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                if (strpos($line, ':') === false) continue;
                
                list($label, $path) = explode(':', $line, 2);
                $label = trim($label);
                $path = trim($path);
                
                $value = $this->process_single_json_filter($json_data, $path);
                if ($value !== null) {
                    $results[$label] = $value;
                }
            }
            
            return empty($results) ? null : $results;
        }

        /**
         * Xử lý single JSON filter với hỗ trợ advanced JSONPath
         */
        private function process_single_json_filter($json_data, $json_filter) {
            $filter = trim($json_filter);

            // Remove $. prefix if present
            if (strpos($filter, '$.') === 0) {
                $filter = substr($filter, 2);
            }

            error_log('AccuFlow Debug: Processing JSONPath: ' . $filter);
            error_log('AccuFlow Debug: JSON data: ' . json_encode($json_data, JSON_UNESCAPED_UNICODE));

            // If filter is empty, return the whole data
            if (empty($filter)) {
                return $json_data;
            }

            // Split the path by dots
            $parts = explode('.', $filter);
            $result = $json_data;

            foreach ($parts as $part) {
                error_log('AccuFlow Debug: Processing part: ' . $part);

                // Handle array access like mediaLinks[0] or stems[1]
                if (preg_match('/^(\w+)\[(\d+)\]$/', $part, $matches)) {
                    $key = $matches[1];
                    $index = intval($matches[2]);

                    error_log('AccuFlow Debug: Array access - key: ' . $key . ', index: ' . $index);

                    if (isset($result[$key]) && is_array($result[$key]) && isset($result[$key][$index])) {
                        $result = $result[$key][$index];
                        error_log('AccuFlow Debug: Array access successful: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
                    } else {
                        error_log('AccuFlow Debug: Array access failed - key not found or not array');
                        return null;
                    }
                } else {
                    // Handle simple property access
                    if (is_array($result) && isset($result[$part])) {
                        $result = $result[$part];
                        error_log('AccuFlow Debug: Property access successful: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
                    } else {
                        error_log('AccuFlow Debug: Property access failed - key "' . $part . '" not found');
                        error_log('AccuFlow Debug: Available keys: ' . (is_array($result) ? implode(', ', array_keys($result)) : 'not an array'));
                        return null;
                    }
                }
            }

            error_log('AccuFlow Debug: Final result: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
            return $result;
        }

        /**
         * Format kết quả cho admin note (hiển thị ngắn gọn)
         */
        private function format_admin_note_result($filtered_result, $product_name, $order_id) {
            if (!is_array($filtered_result)) {
                return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}): " . $filtered_result;
            }
            
            $summary = [];
            foreach ($filtered_result as $label => $value) {
                if (is_string($value) && strlen($value) > 50) {
                    $summary[] = ucfirst($label) . ": " . substr($value, 0, 47) . "...";
                } else {
                    $summary[] = ucfirst($label) . ": " . $value;
                }
            }
            
            return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}). " . implode(", ", $summary);
        }

        /**
         * Format kết quả cho customer note (hiển thị đầy đủ và đẹp)
         */
        private function format_customer_note_result($filtered_result, $product_name) {
            if (!is_array($filtered_result)) {
                return "🎯 Kết quả cho sản phẩm \"{$product_name}\":\n{$filtered_result}";
            }
            
            $formatted = [];
            $formatted[] = "🎯 Thông tin cho sản phẩm \"{$product_name}\"";
            $formatted[] = str_repeat("─", 50);
            
            foreach ($filtered_result as $label => $value) {
                if (is_array($value) || is_object($value)) {
                    $formatted[] = "📋 " . ucfirst($label) . ":";
                    $formatted[] = "   " . json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                } else {
                    $lower_label = strtolower($label);
                    
                    // Custom format cho từng loại
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        if (strpos($lower_label, 'download') !== false || strpos($lower_label, 'media') !== false) {
                            $formatted[] = "🎵 " . ucfirst($label) . ": " . $value;
                            $formatted[] = "   📱 Nhấn để tải: " . $value;
                        } elseif (strpos($lower_label, 'login') !== false || strpos($lower_label, 'url') !== false) {
                            $formatted[] = "� " . ucfirst($label) . ": " . $value;
                        } else {
                            $formatted[] = "🌐 " . ucfirst($label) . ": " . $value;
                        }
                    } elseif (strpos($lower_label, 'password') !== false || strpos($lower_label, 'pass') !== false) {
                        $formatted[] = "� " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'username') !== false || strpos($lower_label, 'user') !== false) {
                        $formatted[] = "� " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'email') !== false) {
                        $formatted[] = "📧 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'id') !== false) {
                        $formatted[] = "� " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'name') !== false || strpos($lower_label, 'title') !== false) {
                        $formatted[] = "📝 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'duration') !== false || strpos($lower_label, 'time') !== false) {
                        $formatted[] = "⏱️ " . ucfirst($label) . ": " . $value;
                    } elseif (strpos($lower_label, 'type') !== false) {
                        $formatted[] = "🏷️ " . ucfirst($label) . ": " . $value;
                    } else {
                        $formatted[] = "� " . ucfirst($label) . ": " . $value;
                    }
                }
            }
            
            $formatted[] = str_repeat("─", 50);
            $formatted[] = "⏰ Cập nhật lúc: " . current_time('H:i \\n\\g\\à\\y d/m/Y');
            
            return implode("\n", $formatted);
        }

        /**
         * Custom format cho email data từ API result
         */
        private function format_email_data_from_api($filtered_result) {
            if (!is_array($filtered_result)) {
                return [
                    'username' => 'Xem chi tiết',
                    'password' => 'Trong ghi chú đơn hàng',
                    'special_message' => 'Thông tin tài khoản: ' . $filtered_result
                ];
            }

            // Tự động map các field phổ biến
            $email_data = [];
            
            foreach ($filtered_result as $label => $value) {
                $lower_label = strtolower($label);
                
                if (strpos($lower_label, 'username') !== false || strpos($lower_label, 'user') !== false) {
                    $email_data['username'] = $value;
                } elseif (strpos($lower_label, 'password') !== false || strpos($lower_label, 'pass') !== false) {
                    $email_data['password'] = $value;
                } elseif (strpos($lower_label, 'login') !== false || strpos($lower_label, 'url') !== false || strpos($lower_label, 'link') !== false) {
                    $email_data['login_url'] = $value;
                }
            }

            // Fallback nếu không tìm thấy username/password
            if (!isset($email_data['username'])) {
                $email_data['username'] = 'Xem chi tiết';
            }
            if (!isset($email_data['password'])) {
                $email_data['password'] = 'Trong ghi chú đơn hàng';
            }

            // Tạo special message từ tất cả các field
            $message_parts = [];
            foreach ($filtered_result as $label => $value) {
                if (is_string($value) && strlen($value) < 100) {
                    $message_parts[] = ucfirst($label) . ": " . $value;
                }
            }
            $email_data['special_message'] = implode(" | ", array_slice($message_parts, 0, 3));

            return $email_data;
        }

        /**
         * Format user input summary for notes and telegram
         */
        private function format_user_input_summary($user_input_data, $product_name, $order_id) {
            $summary = "📋 THÔNG TIN KHÁCH HÀNG NHẬP VÀO - Đơn hàng #{$order_id}\n";
            $summary .= "🎯 Sản phẩm: {$product_name}\n";
            $summary .= "⏰ Thời gian: " . current_time('d/m/Y H:i:s') . "\n\n";

            $summary .= "📧 Email: " . ($user_input_data['email'] ?: '[Không có]') . "\n";
            $summary .= "🔐 Password: " . ($user_input_data['password'] ?: '[Không có]') . "\n";
            $summary .= "🔗 Custom Link: " . ($user_input_data['custom_link'] ?: '[Không có]') . "\n";
            $summary .= "📝 Note: " . ($user_input_data['note'] ?: '[Không có]') . "\n";

            return $summary;
        }

        /**
         * Send Telegram notification for debugging
         */
        private function send_telegram_debug($message) {
            // Telegram Bot Token và Chat ID - thay đổi theo của bạn
            $bot_token = 'YOUR_BOT_TOKEN_HERE'; // Lấy từ @BotFather trên Telegram
            $chat_id = 'YOUR_CHAT_ID_HERE'; // ID của group/channel nhận thông báo

            if (empty($bot_token) || empty($chat_id)) {
                error_log('AccuFlow Debug: Telegram bot token or chat ID not configured');
                return false;
            }

            $telegram_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

            $data = [
                'chat_id' => $chat_id,
                'text' => $message,
                'parse_mode' => 'HTML'
            ];

            $response = wp_remote_post($telegram_url, [
                'body' => $data,
                'timeout' => 10
            ]);

            if (is_wp_error($response)) {
                error_log('AccuFlow Debug: Telegram send error: ' . $response->get_error_message());
                return false;
            }

            return true;
        }
    }
}
