<?php
/**
 * AccuFlow - Google Sheets Integration: <PERSON><PERSON><PERSON> hình plugin
 *
 * @package AccuFlow
 * @subpackage Includes
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Config' ) ) {
    /**
     * Lớp AccuFlow_Config để quản lý cấu hình của plugin.
     */
    class AccuFlow_Config {

        /**
         * @var array Lưu trữ các cài đặt cấu hình hiện tại.
         */
        private $config;

        /**
         * Khởi tạo lớp cấu hình.
         */
        public function __construct() {
            $this->load_config();
        }

        /**
         * Tải các cài đặt cấu hình từ tùy chọn WordPress hoặc sử dụng giá trị mặc định.
         *
         * @return array Các cài đặt cấu hình.
         */
        private function load_config() {
            $default_config = array(
                'spreadsheet_id'                   => '',
                'service_account_type'             => 'service_account',
                'service_account_project_id'       => '',
                'service_account_private_key_id'   => '',
                'service_account_private_key'      => '',
                'service_account_client_email'     => '',
                'service_account_client_id'        => '',
                'service_account_auth_uri'         => 'https://accounts.google.com/o/oauth2/auth',
                'service_account_token_uri'        => 'https://oauth2.googleapis.com/token',
                'service_account_auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
                'service_account_client_x509_cert_url' => '',
                'columns'                          => [
                    'ID'                  => 0,  // Cột A
                    'Username'            => 1,  // Cột B
                    'Password'            => 2,  // Cột C
                    'Login_URL'           => 3,  // Cột D
                    'Status'              => 4,  // Cột E
                    'Order_ID'            => 5,  // Cột F
                    'Sold_Date'           => 6,  // Cột G
                    'Platform'            => 7,  // Cột H (removed Expiration_Date)
                    'Plan'                => 8,  // Cột I
                    'Price'               => 9,  // Cột J
                    'Payment_Status'      => 10, // Cột K
                    'Notes'               => 11, // Cột L
                    'Variation_Attribute' => 12, // Cột M
                ],
                'column_mapping_string'            => 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute',
                'column_indices_string'            => '0|1|2|3|4|5|6|7|8|9|10|11|12',
                'column_config_method'             => 'string', // Always use string method
                'enable_variation_matching'        => false, // Cài đặt mới
                'status_available'                 => 'Available',
                'status_sold'                      => 'Sold',
                // Meta keys mới cho AccuFlow
                'product_fulfillment_method_meta_key' => '_accuflow_fulfillment_method',
                'product_sheet_tab_meta_key'       => '_accuflow_sheets_tab_gid',
                'product_api_endpoint_meta_key'    => '_accuflow_api_endpoint_url',
                'product_api_input_fields_meta_key' => '_accuflow_api_input_fields',
                'product_api_custom_link_regex_meta_key' => '_accuflow_api_custom_link_regex',
                'product_api_json_filter_meta_key' => '_accuflow_api_json_filter', // Meta key mới cho JSON filter
                'product_api_show_frontend_meta_key' => '_accuflow_api_show_frontend', // Meta key cho việc hiển thị frontend
                'product_api_note_filter_meta_key' => '_accuflow_api_note_filter', // Meta key cho filter riêng cho note
                'product_api_placeholders_meta_key' => '_accuflow_api_placeholders', // Meta key cho custom placeholders
                'product_duration_days_meta_key'   => '_accuflow_duration_days', // Meta key mới cho ngày hết hạn
                // Thông tin cửa hàng
                'shop_name'                        => 'AccuFlow Shop',
                'shop_logo_url'                    => ACCUFLOW_PLUGIN_URL . 'assets/images/default-logo.png',
                'support_url'                      => 'https://accuflow.com/support',
                'support_text'                     => 'Trung tâm hỗ trợ AccuFlow',
                'disable_ssl_verify'               => false,
                'test_log_sheet_name'              => 'AccuFlow Test Log',
                // Tùy chọn email người gửi chung
                'sender_email_address'             => get_option( 'admin_email' ),
                'sender_email_name'                => 'AccuFlow Shop',
                // Cấu hình chung cho tất cả email
                'email_header_image'               => get_option( 'woocommerce_email_header_image', '' ),
                'email_footer_text'                => get_option( 'woocommerce_email_footer_text', '{site_title}<br/>{store_address}' ),
                'email_base_color'                 => get_option( 'woocommerce_email_base_color', '#557da1' ),
                'email_background_color'           => get_option( 'woocommerce_email_background_color', '#f0f0f0' ),
                'email_body_background_color'      => get_option( 'woocommerce_email_body_background_color', '#ffffff' ),
                'email_text_color'                 => get_option( 'woocommerce_email_text_color', '#333333' ),
                'email_footer_text_color'          => get_option( 'woocommerce_email_footer_text_color', '#787c82' ),
                'email_font_family'                => get_option( 'woocommerce_email_font_family', 'Helvetica, Arial, sans-serif' ),
                'email_header_image_width'         => get_option( 'woocommerce_email_header_image_width', 125 ),
                'email_header_alignment'           => get_option( 'woocommerce_email_header_alignment', 'center' ),
                // Removed email configurations to avoid WooCommerce conflicts
            );

            $saved_settings = get_option( 'accuflow_settings', array() );
            $this->config = array_replace_recursive( $default_config, $saved_settings );

            if ( isset( $this->config['service_account_private_key'] ) ) {
                $this->config['service_account_private_key'] = str_replace( '\\n', "\n", $this->config['service_account_private_key'] );
            }

            return $this->config;
        }

        /**
         * Lấy toàn bộ cấu hình.
         *
         * @return array Các cài đặt cấu hình.
         */
        public function get_config() {
            return $this->config;
        }

        /**
         * Lấy một giá trị cấu hình cụ thể.
         *
         * @param string $key Khóa của cài đặt cần lấy.
         * @param mixed $default Giá trị mặc định nếu khóa không tồn tại.
         * @return mixed Giá trị của cài đặt hoặc giá trị mặc định.
         */
        public function get_setting( $key, $default = null ) {
            return isset( $this->config[ $key ] ) ? $this->config[ $key ] : $default;
        }

        /**
         * Lưu các cài đặt cấu hình mới.
         *
         * @param array $new_settings Mảng các cài đặt mới cần lưu.
         * @return bool True nếu cài đặt được cập nhật, False nếu không.
         */
        public function save_settings( $new_settings ) {
            if ( isset( $new_settings['service_account_private_key'] ) ) {
                $new_settings['service_account_private_key'] = str_replace( "\n", '\\n', $new_settings['service_account_private_key'] );
            }

            $current_settings = $this->get_config();
            $merged_settings = array_replace_recursive($current_settings, $new_settings);

            // Debug log for column mapping
            if (isset($new_settings['column_mapping_string']) || isset($new_settings['columns'])) {
                error_log('AccuFlow Debug: Saving settings with column data:');
                error_log('- column_mapping_string: ' . ($new_settings['column_mapping_string'] ?? 'not set'));
                error_log('- column_indices_string: ' . ($new_settings['column_indices_string'] ?? 'not set'));
                error_log('- columns: ' . print_r($new_settings['columns'] ?? [], true));
            }

            $updated = update_option( 'accuflow_settings', $merged_settings );
            $this->load_config();

            // Debug log result
            if (isset($new_settings['column_mapping_string']) || isset($new_settings['columns'])) {
                error_log('AccuFlow Debug: Settings saved. Updated: ' . ($updated ? 'YES' : 'NO'));
                $saved_settings = get_option('accuflow_settings');
                error_log('AccuFlow Debug: Verified saved column_mapping_string: ' . ($saved_settings['column_mapping_string'] ?? 'not found'));
                error_log('AccuFlow Debug: Verified saved column_indices_string: ' . ($saved_settings['column_indices_string'] ?? 'not found'));
            }

            return $updated;
        }
    }
}
