<?php
/**
 * AccuFlow - Google Sheets Integration: Partial cho hiển thị liên kết sản phẩm.
 *
 * @package AccuFlow
 * @subpackage Admin/Partials
 * @since 2.5.0
 *
 * @var WP_Query $product_query Vòng lặp sản phẩm.
 * @var int $total_pages Tổng số trang.
 * @var AccuFlow_Admin $this Đối tượng admin.
 * @var int $paged Trang hiện tại.
 * @var string $s Từ khóa tìm kiếm.
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<form method="get">
    <input type="hidden" name="page" value="accuflow-sheets-integration">
    <input type="hidden" name="tab" value="product-links">
    <p class="search-box">
        <label class="screen-reader-text" for="post-search-input">Tìm kiếm sản phẩm:</label>
        <input type="search" id="post-search-input" name="s" value="<?php echo esc_attr( $s ); ?>">
        <input type="submit" id="search-submit" class="button" value="Tìm kiếm sản phẩm">
    </p>
</form>

<form method="post" action="?page=accuflow-sheets-integration&tab=product-links">
    <?php wp_nonce_field( 'accuflow_save_product_links_nonce' ); ?>
    <input type="hidden" name="current_tab" value="product-links">

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" class="manage-column column-name">Sản phẩm</th>
                <th scope="col" class="manage-column column-sku" style="width: 10%;">SKU</th>
                <th scope="col" class="manage-column" style="width: 15%;">Phương thức phân phối</th>
                <th scope="col" class="manage-column">Cấu hình chi tiết</th>
                <th scope="col" class="manage-column" style="width: 12%;">Hành động</th>
            </tr>
        </thead>
        <tbody id="the-list">
            <?php
                // Use the admin class method to render products (admin_instance is passed from admin class)
                if ($admin_instance && method_exists($admin_instance, 'render_product_list_with_variations')) {
                    $admin_instance->render_product_list_with_variations( $product_query );
                } else {
                    echo '<tr><td colspan="5">Lỗi: Không thể tải danh sách sản phẩm.</td></tr>';
                }
            ?>
        </tbody>
        <tfoot>
            <tr>
                <th scope="col" class="manage-column column-name">Sản phẩm</th>
                <th scope="col" class="manage-column column-sku">SKU</th>
                <th scope="col" class="manage-column">Phương thức phân phối</th>
                <th scope="col" class="manage-column">Cấu hình chi tiết</th>
                <th scope="col" class="manage-column">Hành động</th>
            </tr>
        </tfoot>
    </table>

    <div class="tablenav bottom">
        <div class="tablenav-pages">
            <span class="displaying-num"><?php echo esc_html( $product_query->found_posts ); ?> sản phẩm</span>
            <?php
            $page_links = paginate_links( array(
                'base'      => add_query_arg( ['paged' => '%#%', 's' => $s, 'tab' => 'product-links'] ),
                'format'    => '',
                'prev_text' => '&laquo;',
                'next_text' => '&raquo;',
                'total'     => $total_pages,
                'current'   => $paged,
            ) );

            if ( $page_links ) {
                echo '<span class="pagination-links">' . $page_links . '</span>';
            }
            ?>
        </div>
        <br class="clear">
    </div>

    <p class="submit">
        <input type="submit" name="accuflow_save_product_links" class="button button-primary" value="Lưu tất cả thay đổi">
    </p>
</form>

<!-- API Settings Modal -->
<div id="accuflow-api-settings-modal" class="accuflow-modal" style="display: none;">
    <div class="accuflow-modal-content">
        <div class="accuflow-modal-header">
            <div class="modal-header-content">
                <div class="modal-title-section">
                    <h2>⚙️ API Configuration</h2>
                    <p class="modal-subtitle">Configure API endpoint, input fields and JSON filters for this product</p>
                </div>
                <button class="accuflow-modal-close" type="button">
                    <span class="close-icon">✕</span>
                </button>
            </div>
        </div>
        <div class="accuflow-modal-body">
            <form id="accuflow-api-settings-form">
                <!-- API Endpoint Section -->
                <div class="config-section">
                    <div class="section-icon">🔗</div>
                    <div class="section-content">
                        <h3>API Endpoint</h3>
                        <p class="section-description">Set up your API endpoint URL with dynamic variables</p>
                        <div class="input-group">
                            <label for="modal-api-endpoint">API URL</label>
                            <input type="text" id="modal-api-endpoint" placeholder="http://**************:3000/api/artlist?url={custom_link}&api_key=9e2aaf401f944e84bc7498c54e0a8f17">
                            <div class="help-text">
                                <span class="help-label">Available variables:</span>
                                <span class="variable-tag">{email}</span>
                                <span class="variable-tag">{password}</span>
                                <span class="variable-tag">{custom_link}</span>
                                <span class="variable-tag">{note}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Input Fields Section -->
                <div class="config-section">
                    <div class="section-icon">👤</div>
                    <div class="section-content">
                        <h3>Input Fields (Frontend)</h3>
                        <p class="section-description">Choose which fields customers will see on the product page</p>

                        <div class="field-selection">
                            <div class="field-option">
                                <label class="field-checkbox">
                                    <input type="checkbox" value="email" class="modal-input-field">
                                    <span class="checkmark"></span>
                                    <span class="field-label">Email</span>
                                </label>
                            </div>
                            <div class="field-option">
                                <label class="field-checkbox">
                                    <input type="checkbox" value="password" class="modal-input-field">
                                    <span class="checkmark"></span>
                                    <span class="field-label">Password</span>
                                </label>
                            </div>
                            <div class="field-option">
                                <label class="field-checkbox">
                                    <input type="checkbox" value="custom_link" class="modal-input-field">
                                    <span class="checkmark"></span>
                                    <span class="field-label">Custom Link</span>
                                </label>
                            </div>
                            <div class="field-option">
                                <label class="field-checkbox">
                                    <input type="checkbox" value="note" class="modal-input-field">
                                    <span class="checkmark"></span>
                                    <span class="field-label">Note</span>
                                </label>
                            </div>
                        </div>

                        <div class="placeholder-configs">
                            <h4>Customize Placeholder Text</h4>
                            <div class="placeholder-field" data-field="email" style="display: none;">
                                <div class="input-group">
                                    <label>Email Placeholder</label>
                                    <input type="text" id="modal-placeholder-email" placeholder="Enter your email">
                                </div>
                            </div>
                            <div class="placeholder-field" data-field="password" style="display: none;">
                                <div class="input-group">
                                    <label>Password Placeholder</label>
                                    <input type="text" id="modal-placeholder-password" placeholder="Enter password">
                                </div>
                            </div>
                            <div class="placeholder-field" data-field="custom_link" style="display: none;">
                                <div class="input-group">
                                    <label>Custom Link Placeholder</label>
                                    <input type="text" id="modal-placeholder-custom_link" placeholder="https://artlist.io/your-link">
                                    <input type="hidden" id="modal-regex-custom_link" value="(.+)">
                                    <div class="help-text">Regex pattern will auto-detect based on your placeholder format</div>
                                </div>
                            </div>
                            <div class="placeholder-field" data-field="note" style="display: none;">
                                <div class="input-group">
                                    <label>Note Placeholder</label>
                                    <input type="text" id="modal-placeholder-note" placeholder="Additional notes (optional)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- JSON Response Filter Section -->
                <div class="config-section">
                    <div class="section-icon">🔍</div>
                    <div class="section-content">
                        <h3>JSON Response Filter</h3>
                        <p class="section-description">Define how to extract and display data from API response</p>

                        <div class="input-group">
                            <label>Template Format</label>
                            <textarea id="modal-json-filter" rows="6" placeholder="Link tải xuống: {$.mediaLinks}&#10;Tên nghệ sĩ: {$.metadata.artistName}&#10;Tên bài hát: {$.metadata.songTitle}&#10;Thời lượng: {$.metadata.duration}&#10;ID: {$.id}"></textarea>
                            <div class="help-text">
                                <span class="help-label">JSONPath Syntax Guide:</span>
                                <div class="example-tags">
                                    <span class="variable-tag">{$.field}</span> → Root level field
                                    <span class="variable-tag">{$.object.field}</span> → Nested field
                                    <span class="variable-tag">{$.array}</span> → Entire array
                                    <span class="variable-tag">{$.array[0]}</span> → First element
                                </div>
                                <div class="help-note">
                                    <strong>Mỗi dòng = 1 item hiển thị.</strong> Array sẽ được clean tự động (bỏ [""]). URL dài sẽ được truncate.
                                </div>

                                <details style="margin-top: 10px;">
                                    <summary style="cursor: pointer; font-weight: bold; color: #007cba;">📖 Chi tiết cú pháp và ví dụ</summary>
                                    <div style="margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 12px;">
                                        <h4 style="margin: 0 0 10px 0; color: #333;">🎯 Template cho GETLINK/Artlist:</h4>
                                        <pre style="background: #fff; padding: 10px; border-radius: 3px; margin: 5px 0;">Link tải xuống: {$.mediaLinks}
Tên nghệ sĩ: {$.metadata.artistName}
Tên bài hát: {$.metadata.songTitle}
Thời lượng: {$.metadata.duration}
ID: {$.id}
Trạng thái: {$.success}</pre>

                                        <h4 style="margin: 15px 0 10px 0; color: #333;">🔍 Cú pháp nâng cao:</h4>
                                        <pre style="background: #fff; padding: 10px; border-radius: 3px; margin: 5px 0;">// Array elements
Link chính: {$.mediaLinks[0]}
Link backup: {$.mediaLinks[1]}

// Nested objects
Thể loại: {$.metadata.genre.primary}
Ngày tạo: {$.metadata.created.date}

// Conditional fields
Lỗi (nếu có): {$.error}
Thông báo: {$.message}</pre>

                                        <h4 style="margin: 15px 0 10px 0; color: #333;">✅ Output sẽ được format clean:</h4>
                                        <pre style="background: #fff; padding: 10px; border-radius: 3px; margin: 5px 0;">🔗 Link tải xuống: https://example.com/very-long-url...
👤 Tên nghệ sĩ: Adrian Berenguer
🎵 Tên bài hát: Pastor - Presto
⏱️ Thời lượng: 2:44</pre>
                                    </div>
                                </details>
                            </div>
                        </div>

                        <div class="test-section">
                            <button type="button" class="test-button" id="modal-test-api">
                                <span class="test-icon">🧪</span>
                                Test API
                            </button>
                            <div id="modal-test-result" class="test-result"></div>
                        </div>
                    </div>
                </div>

                <!-- Note Filter Section -->
                <div class="config-section">
                    <div class="section-icon">📝</div>
                    <div class="section-content">
                        <h3>Note Filter (Optional)</h3>
                        <p class="section-description">Customize how order notes are displayed</p>

                        <div class="input-group">
                            <label>Filter for Order Note</label>
                            <textarea id="modal-note-filter" rows="3" placeholder="Ghi chú: {$.metadata.songName}&#10;Thời lượng: {$.metadata.duration}"></textarea>
                            <div class="help-text">Leave empty if not needed. This will be used for order notes display.</div>
                        </div>

                        <div class="toggle-option">
                            <label class="toggle-checkbox">
                                <input type="checkbox" id="modal-show-frontend">
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Show input fields on frontend</span>
                            </label>
                            <div class="help-text">Customers will see input fields when viewing the product.</div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="accuflow-modal-footer">
            <button type="button" class="button-secondary" id="accuflow-cancel-api-settings">Cancel</button>
            <button type="button" class="button-primary" id="accuflow-save-api-settings">Save Configuration</button>
        </div>
    </div>
</div>
