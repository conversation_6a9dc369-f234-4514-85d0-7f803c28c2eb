# Troubleshooting AccuFlow Extensions

## Vấn đề: Không thấy nút Quick Payment Buttons

### Bước 1: Kiểm tra plugin đã được kích hoạt
1. Vào **WordPress Admin** → **Plugins**
2. T<PERSON><PERSON> **"AccuFlow Extensions"**
3. <PERSON><PERSON><PERSON> bảo plugin đã được **Activated**

### Bước 2: Ki<PERSON>m tra WooCommerce
1. <PERSON><PERSON><PERSON> b<PERSON>o **WooCommerce plugin** đã được kích hoạt
2. V<PERSON>o trang sản phẩm WooCommerce (không phải post thường)
3. Sản phẩm phải có trạng thái **"In stock"** và **purchasable**

### Bước 3: Bật Debug Mode
Thêm vào file `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
```

### Bước 4: Kiểm tra Debug Info
1. <PERSON><PERSON><PERSON> trang sản phẩm WooCommerce
2. Nếu bạn là admin, sẽ thấy thông tin debug ở đầu trang
3. Kiểm tra các mục sau:
   - ✅ Class exists: YES
   - ✅ WooCommerce active: YES  
   - ✅ Product page: YES
   - ✅ Hooks registered: Tất cả phải có ✅
   - ✅ Asset files: CSS và JS phải EXISTS

### Bước 5: Kiểm tra Theme Compatibility
Một số theme có thể override WooCommerce hooks:

1. **Test với theme mặc định:**
   - Chuyển tạm sang theme Twenty Twenty-Three
   - Kiểm tra xem nút có hiển thị không

2. **Kiểm tra theme hooks:**
   - Theme có thể đã remove hook `woocommerce_single_product_summary`
   - Liên hệ developer theme để hỗ trợ

### Bước 6: Kiểm tra CSS Conflicts
1. **Mở Developer Tools** (F12)
2. **Kiểm tra Elements:**
   ```html
   <div class="quick-payment-buttons" data-product-id="123">
       <button class="quick-payment-buy-button">Mua ngay</button>
       <button class="quick-payment-addtocart-button">Thêm vào giỏ</button>
   </div>
   ```
3. **Nếu HTML có nhưng không thấy:**
   - Kiểm tra CSS có bị override không
   - Thêm CSS tùy chỉnh trong admin

### Bước 7: Kiểm tra JavaScript Errors
1. **Mở Console** (F12 → Console)
2. **Tìm lỗi JavaScript:**
   - `qpb_data is not defined`
   - `jQuery is not defined`
   - Các lỗi AJAX

## Vấn đề: Nút hiển thị nhưng không hoạt động

### Kiểm tra AJAX
1. **Mở Network tab** trong Developer Tools
2. **Click nút** và xem request:
   - URL: `/wp-admin/admin-ajax.php`
   - Action: `qpb_buy_now` hoặc `qpb_add_to_cart`
   - Response: `{"success":true}` hoặc error

### Kiểm tra Nonce
- Nếu lỗi 403 hoặc "nonce verification failed"
- Clear cache và refresh trang

## Vấn đề: Linked Products Buttons không hiển thị

### Kiểm tra Cross-sell Products
1. **Vào WooCommerce** → **Products** → **Edit Product**
2. **Scroll xuống "Linked Products"**
3. **Thêm sản phẩm vào "Cross-sells"**
4. **Save product**

### Kiểm tra Custom Button Names
1. **Trong product edit page**
2. **Tìm field "Tên nút liên kết"**
3. **Nhập tên tùy chỉnh** (tùy chọn)

## Vấn đề: Admin Menu không hiển thị

### Kiểm tra Permissions
- User phải có quyền `manage_woocommerce`
- Hoặc là Administrator

### Kiểm tra WooCommerce Menu
- Menu sẽ xuất hiện trong **WooCommerce** → **AccuFlow Extensions**
- Không phải menu riêng

## Vấn đề: Settings không lưu

### Kiểm tra Nonce
- Đảm bảo form có nonce field
- Clear cache sau khi save

### Kiểm tra Database
```sql
SELECT * FROM wp_options WHERE option_name LIKE '%qpb%' OR option_name LIKE '%choacc_linked%';
```

## Debug Commands

### Kiểm tra Plugin Status
```php
// Thêm vào functions.php tạm thời
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        echo '<div style="position:fixed;bottom:0;right:0;background:#fff;padding:10px;border:1px solid #ccc;">';
        echo 'QPB Class: ' . (class_exists('Quick_Payment_Buttons') ? 'YES' : 'NO') . '<br>';
        echo 'LPB Class: ' . (class_exists('Linked_Products_Buttons') ? 'YES' : 'NO') . '<br>';
        echo 'WC Active: ' . (class_exists('WooCommerce') ? 'YES' : 'NO') . '<br>';
        echo '</div>';
    }
});
```

### Force Reload Assets
```php
// Thêm vào functions.php để force reload CSS/JS
add_action('wp_enqueue_scripts', function() {
    wp_dequeue_style('quick-payment-button-style');
    wp_dequeue_script('quick-payment-button-script');
}, 5);
```

## Liên hệ hỗ trợ

Nếu vẫn gặp vấn đề, hãy cung cấp:

1. **WordPress version**
2. **WooCommerce version** 
3. **Theme name và version**
4. **Plugin list** (active plugins)
5. **Debug info** từ trang sản phẩm
6. **Console errors** (nếu có)
7. **Screenshots** của vấn đề

Email: <EMAIL>
