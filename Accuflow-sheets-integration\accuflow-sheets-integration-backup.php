<?php
/**
 * Plugin Name: AccuFlow Extensions
 * Plugin URI:  https://accuflow.com/
 * Description: Bộ plugin tổng hợp bao gồm: Google Sheets Integration, Quick Payment Buttons, và WooCommerce Linked Products Buttons. Tự động phân phối tà<PERSON>, tù<PERSON> chỉnh nút thanh toán và hiển thị sản phẩm liên quan.
 * Version:     3.0.0
 * Author:      AccuFlow Team
 * Author URI:  https://accuflow.com
 * License:     GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: accuflow-extensions
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Định nghĩa các constants
define( 'ACCUFLOW_EXTENSIONS_VERSION', '3.0.0' );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_FILE', __FILE__ );
define( 'ACCUFLOW_VENDOR_DIR', ACCUFLOW_EXTENSIONS_PLUGIN_DIR . 'vendor/' );

// Backward compatibility constants
define( 'ACCUFLOW_VERSION', ACCUFLOW_EXTENSIONS_VERSION );
define( 'ACCUFLOW_PLUGIN_DIR', ACCUFLOW_EXTENSIONS_PLUGIN_DIR );
define( 'ACCUFLOW_PLUGIN_URL', ACCUFLOW_EXTENSIONS_PLUGIN_URL );

// Kiểm tra xem thư viện Google API Client đã được tải chưa
if ( file_exists( ACCUFLOW_VENDOR_DIR . 'autoload.php' ) ) {
    require_once ACCUFLOW_VENDOR_DIR . 'autoload.php';
} else {
    // Thông báo lỗi nếu thư viện không tìm thấy (quan trọng!)
    add_action( 'admin_notices', function() {
        echo '<div class="notice notice-error"><p><strong>AccuFlow - Google Sheets Integration:</strong> Thư viện Google API Client không tìm thấy. Vui lòng chạy <code>composer install</code> trong thư mục plugin hoặc tải thư mục <code>vendor</code> lên.</p></div>';
    });
    return; // Dừng kích hoạt plugin nếu không có thư viện
}

/**
 * Tải các file cần thiết
 */
// AccuFlow Google Sheets Integration
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-config.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-utils.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-google-api.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-email-template.php';
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-order-fulfillment.php';

// Quick Payment Buttons
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-quick-payment-buttons.php';

// WooCommerce Linked Products Buttons
require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-linked-products-buttons.php';

// Test file (only in debug mode)
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once ACCUFLOW_PLUGIN_DIR . 'test-plugin-integration.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'debug-quick-payment.php';
}

/**
 * Hàm khởi tạo plugin
 */
function accuflow_extensions_run() {
    // Force include classes again
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-quick-payment-buttons.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-linked-products-buttons.php';

    // Debug: Log class existence
    error_log( 'AccuFlow Debug: Quick_Payment_Buttons exists: ' . ( class_exists( 'Quick_Payment_Buttons' ) ? 'YES' : 'NO' ) );
    error_log( 'AccuFlow Debug: Linked_Products_Buttons exists: ' . ( class_exists( 'Linked_Products_Buttons' ) ? 'YES' : 'NO' ) );

    // AccuFlow Google Sheets Integration
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils( $config );
    $google_api = new AccuFlow_Google_API( $config, $utils );
    // Removed email template to avoid WooCommerce conflicts
    $order_fulfillment = new AccuFlow_Order_Fulfillment( $config, $google_api );
    $order_fulfillment->init(); // Khởi tạo các hooks

    // Quick Payment Buttons
    if ( class_exists( 'Quick_Payment_Buttons' ) ) {
        $quick_payment = new Quick_Payment_Buttons();
        $quick_payment->init();
        error_log( 'AccuFlow Debug: Quick Payment Buttons initialized' );
    } else {
        error_log( 'AccuFlow Debug: Quick_Payment_Buttons class not found!' );
    }

    // WooCommerce Linked Products Buttons
    if ( class_exists( 'Linked_Products_Buttons' ) ) {
        $linked_products = new Linked_Products_Buttons();
        $linked_products->init();
        error_log( 'AccuFlow Debug: Linked Products Buttons initialized' );
    } else {
        error_log( 'AccuFlow Debug: Linked_Products_Buttons class not found!' );
    }

    if ( is_admin() ) {
        require_once ACCUFLOW_PLUGIN_DIR . 'admin/class-accuflow-admin.php';
        $admin = new AccuFlow_Admin( $config, $google_api, $utils, $order_fulfillment );
        $admin->init(); // Khởi tạo các hooks admin

        // Set global variable for use in included files
        global $accuflow_admin;
        $accuflow_admin = $admin;
    }
}

// Khởi chạy plugin
add_action( 'init', 'accuflow_extensions_run' );

// Backward compatibility
function accuflow_run() {
    accuflow_extensions_run();
}