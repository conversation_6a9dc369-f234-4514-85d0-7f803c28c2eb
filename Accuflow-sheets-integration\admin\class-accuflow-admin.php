<?php
/**
 * AccuFlow - Google Sheets Integration: <PERSON><PERSON><PERSON> quản lý giao diện Admin
 *
 * @package AccuFlow
 * @subpackage Admin
 * @since 2.5.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'AccuFlow_Admin' ) ) {
    /**
     * Lớp AccuFlow_Admin xử lý tất cả các chức năng và giao diện trong khu vực admin.
     */
    class AccuFlow_Admin {

        private $config;
        private $google_api;
        private $utils;
        private $order_fulfillment;

        public function __construct( AccuFlow_Config $config, AccuFlow_Google_API $google_api, AccuFlow_Utils $utils, AccuFlow_Order_Fulfillment $order_fulfillment ) {
            $this->config            = $config;
            $this->google_api        = $google_api;
            $this->utils             = $utils;
            $this->order_fulfillment = $order_fulfillment;
        }

        public function init() {
            // Removed WooCommerce email redirect hook to avoid conflicts
            add_action( 'admin_menu', [$this, 'add_admin_menu'] );
            add_action( 'admin_enqueue_scripts', [$this, 'enqueue_admin_scripts'] );
            add_action( 'wp_ajax_accuflow_test_connection_ajax', [$this, 'handle_test_connection_ajax'] );
            add_action( 'wp_ajax_accuflow_detailed_diagnosis_ajax', [$this, 'handle_detailed_diagnosis_ajax'] );
            add_action( 'wp_ajax_accuflow_download_cacert_ajax', [$this, 'handle_download_cacert_ajax'] );
            add_action( 'wp_ajax_accuflow_test_product_link_ajax', [$this, 'handle_test_product_link_ajax'] );
            add_action( 'wp_ajax_accuflow_write_connection_log_ajax', [$this, 'handle_write_connection_log_ajax'] );
            add_action( 'wp_ajax_accuflow_test_api_call_ajax', [$this, 'handle_test_api_call_ajax'] );
            // Removed email AJAX handlers to avoid WooCommerce conflicts
            add_action( 'wp_ajax_accuflow_auto_save_fulfillment_method', [$this, 'handle_auto_save_fulfillment_method_ajax'] );
            add_action( 'wp_ajax_accuflow_get_product_list_ajax', [$this, 'handle_get_product_list_ajax'] );

            // Debug tools AJAX handlers
            add_action( 'wp_ajax_accuflow_reprocess_order_ajax', [$this, 'handle_reprocess_order_ajax'] );
            add_action( 'wp_ajax_accuflow_set_custom_link_ajax', [$this, 'handle_set_custom_link_ajax'] );

            // Quick Payment Buttons AJAX handlers
            add_action('wp_ajax_qpb_add_to_cart', [$this, 'handle_qpb_add_to_cart']);
            add_action('wp_ajax_nopriv_qpb_add_to_cart', [$this, 'handle_qpb_add_to_cart']);
            add_action('wp_ajax_qpb_buy_now', [$this, 'handle_qpb_buy_now']);
            add_action('wp_ajax_nopriv_qpb_buy_now', [$this, 'handle_qpb_buy_now']);
        }

        public function add_admin_menu() {
            add_submenu_page(
                'woocommerce',
                'AccuFlow Extensions',
                'AccuFlow Extensions',
                'manage_options',
                'accuflow-extensions',
                [$this, 'admin_page_content']
            );

            // Add debug tools submenu
            add_submenu_page(
                'woocommerce',
                'AccuFlow - Debug Tools',
                'AccuFlow Debug',
                'manage_options',
                'accuflow-debug-tools',
                [$this, 'debug_tools_page']
            );
        }

        // Removed WooCommerce email redirect to avoid conflicts

        public function enqueue_admin_scripts() {
            if ( isset( $_GET['page'] ) && $_GET['page'] === 'accuflow-extensions' ) {
                wp_enqueue_style( 'wp-color-picker' );
                wp_enqueue_style( 'accuflow-admin-styles', ACCUFLOW_PLUGIN_URL . 'admin/css/admin-styles.css', [], ACCUFLOW_VERSION );
                wp_enqueue_script( 'accuflow-admin-scripts', ACCUFLOW_PLUGIN_URL . 'admin/js/admin-scripts.js', ['jquery', 'wp-color-picker'], ACCUFLOW_VERSION, true );
                wp_localize_script( 'accuflow-admin-scripts', 'accuflow_ajax_object', [
                    'ajax_url'                            => admin_url( 'admin-ajax.php' ),
                    'test_connection_nonce'               => wp_create_nonce( 'accuflow_test_connection_nonce' ),
                    'detailed_diagnosis_nonce'            => wp_create_nonce( 'accuflow_detailed_diagnosis_nonce' ),
                    'download_cacert_nonce'               => wp_create_nonce( 'accuflow_download_cacert_nonce' ),
                    'test_product_link_nonce'             => wp_create_nonce( 'accuflow_test_product_link_nonce' ),
                    'write_connection_log_nonce'          => wp_create_nonce( 'accuflow_write_connection_log_nonce' ),
                    'test_api_call_nonce'                 => wp_create_nonce( 'accuflow_test_api_call_nonce' ),
                    'email_preview_nonce'                 => wp_create_nonce( 'accuflow_email_preview_nonce' ),
                    'send_test_email_nonce'               => wp_create_nonce( 'accuflow_send_test_email_nonce' ),
                    'save_product_links_nonce'            => wp_create_nonce( 'accuflow_save_product_links_nonce' ),
                    'get_product_list_nonce'              => wp_create_nonce( 'accuflow_get_product_list_nonce' ),
                    'debug_order_nonce'                   => wp_create_nonce( 'accuflow_debug_order_nonce' ),
                    'reprocess_order_nonce'               => wp_create_nonce( 'accuflow_reprocess_order_nonce' ),
                    'fix_product_config_nonce'            => wp_create_nonce( 'accuflow_fix_product_config_nonce' ),
                    'set_custom_link_nonce'               => wp_create_nonce( 'accuflow_set_custom_link_nonce' ),
                    'current_s_query'                     => isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '',
                    'current_paged'                       => isset($_GET['paged']) ? absint($_GET['paged']) : 1,
                ]);
            }
        }
        
        public function admin_page_content() {
            $current_settings = $this->config->get_config();
            $message = '';
            $message_type = '';



            // Xử lý lưu cài đặt Quick Payment Buttons
            if ( isset( $_POST['save_quick_payment_settings'] ) ) {
                check_admin_referer('quick_payment_settings_nonce');

                $qpb_enabled = isset($_POST['qpb_enabled']) ? 1 : 0;
                $custom_css = isset($_POST['qpb_custom_css']) ? wp_unslash($_POST['qpb_custom_css']) : '';

                update_option('qpb_enabled', $qpb_enabled);
                update_option('qpb_custom_css', $custom_css);

                $message = 'Cài đặt Quick Payment Buttons đã được lưu thành công!';
                $message_type = 'success';

                $redirect_url = admin_url('admin.php?page=accuflow-extensions&tab=quick-payment&message=' . urlencode($message) . '&message_type=' . $message_type);
                wp_redirect($redirect_url);
                exit;
            }

            // Xử lý lưu cài đặt Linked Products Buttons
            if ( isset( $_POST['save_linked_products_settings'] ) ) {
                check_admin_referer('linked_products_settings_nonce');

                $lpb_enabled = isset($_POST['lpb_enabled']) ? 1 : 0;
                $custom_css = isset($_POST['choacc_linked_buttons_custom_css']) ? wp_unslash($_POST['choacc_linked_buttons_custom_css']) : '';
                $group_label = isset($_POST['choacc_linked_buttons_group_label']) ? sanitize_text_field($_POST['choacc_linked_buttons_buttons_group_label']) : 'Chọn gói sản phẩm';

                update_option('lpb_enabled', $lpb_enabled);
                update_option('choacc_linked_buttons_custom_css', $custom_css);
                update_option('choacc_linked_buttons_group_label', $group_label);

                $message = 'Cài đặt Linked Products Buttons đã được lưu thành công!';
                $message_type = 'success';

                $redirect_url = admin_url('admin.php?page=accuflow-extensions&tab=linked-products&message=' . urlencode($message) . '&message_type=' . $message_type);
                wp_redirect($redirect_url);
                exit;
            }

            // Check for success message from URL
            if (isset($_GET['saved']) && $_GET['saved'] === '1') {
                $message = 'Cài đặt đã được lưu thành công!';
                $message_type = 'success';
            } elseif (isset($_GET['message']) && isset($_GET['message_type'])) {
                $message = sanitize_text_field($_GET['message']);
                $message_type = sanitize_text_field($_GET['message_type']);
            }

            // Xử lý lưu Cài đặt Chung
            if ( isset( $_POST['accuflow_save_settings'] ) && check_admin_referer( 'accuflow_save_settings_nonce' ) ) {
                $new_settings = [
                    'spreadsheet_id'                   => sanitize_text_field( $_POST['spreadsheet_id'] ),
                    'service_account_project_id'       => sanitize_text_field( $_POST['service_account_project_id'] ?? '' ),
                    'service_account_private_key_id'   => sanitize_text_field( $_POST['service_account_private_key_id'] ?? '' ),
                    'service_account_private_key'      => sanitize_textarea_field( $_POST['service_account_private_key'] ?? '' ),
                    'service_account_client_email'     => sanitize_email( $_POST['service_account_client_email'] ?? '' ),
                    'service_account_client_id'        => sanitize_text_field( $_POST['service_account_client_id'] ?? '' ),
                    'service_account_client_x509_cert_url' => esc_url_raw( $_POST['service_account_client_x509_cert_url'] ?? '' ),
                    'status_available'                 => sanitize_text_field( $_POST['status_available'] ),
                    'status_sold'                      => sanitize_text_field( $_POST['status_sold'] ),
                    'shop_name'                        => sanitize_text_field( $_POST['shop_name'] ),
                    'shop_logo_url'                    => esc_url_raw( $_POST['shop_logo_url'] ),
                    'support_url'                      => esc_url_raw( $_POST['support_url'] ),
                    'support_text'                     => sanitize_text_field( $_POST['support_text'] ),
                    'disable_ssl_verify'               => isset( $_POST['disable_ssl_verify'] ),
                    'test_log_sheet_name'              => sanitize_text_field( $_POST['test_log_sheet_name'] ?? 'AccuFlow Test Log' ),
                    'enable_variation_matching'        => isset( $_POST['enable_variation_matching'] ),
                ];

                // Handle column configuration (String method only)
                $column_mapping_string = sanitize_text_field($_POST['column_mapping_string'] ?? '');
                $column_indices_string = sanitize_text_field($_POST['column_indices_string'] ?? '');

                $columns = [];

                // Always use string method
                $new_settings['column_config_method'] = 'string';

                if (!empty($column_mapping_string) && !empty($column_indices_string)) {
                    // Parse dynamic string format
                    $column_names = array_map('trim', explode('|', $column_mapping_string));
                    $column_indices = array_map('trim', explode('|', $column_indices_string));

                    // Build columns array dynamically
                    for ($i = 0; $i < min(count($column_names), count($column_indices)); $i++) {
                        $name = sanitize_key($column_names[$i]);
                        $index = absint($column_indices[$i]);
                        if (!empty($name)) {
                            $columns[$name] = $index;
                        }
                    }

                    // Save both mapping string and indices string
                    $new_settings['column_mapping_string'] = $column_mapping_string;
                    $new_settings['column_indices_string'] = $column_indices_string;

                    // Debug log
                    error_log('AccuFlow Debug: Saving column mapping - Names: ' . $column_mapping_string . ', Indices: ' . $column_indices_string);
                    error_log('AccuFlow Debug: Parsed columns: ' . print_r($columns, true));
                } else {
                    // If strings are empty, use default values
                    $default_mapping = 'ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute';
                    $default_indices = '0|1|2|3|4|5|6|7|8|9|10|11|12|13';

                    $new_settings['column_mapping_string'] = $default_mapping;
                    $new_settings['column_indices_string'] = $default_indices;

                    // Parse default values
                    $column_names = array_map('trim', explode('|', $default_mapping));
                    $column_indices = array_map('trim', explode('|', $default_indices));

                    for ($i = 0; $i < min(count($column_names), count($column_indices)); $i++) {
                        $name = sanitize_key($column_names[$i]);
                        $index = absint($column_indices[$i]);
                        if (!empty($name)) {
                            $columns[$name] = $index;
                        }
                    }
                }

                $new_settings['columns'] = $columns;

                $save_result = $this->config->save_settings( $new_settings );

                // Debug log before redirect
                error_log('AccuFlow Debug: Save completed. Result: ' . ($save_result ? 'SUCCESS' : 'FAILED'));

                // Verify settings were saved before redirect
                $verification = get_option('accuflow_settings');
                error_log('AccuFlow Debug: Pre-redirect verification - column_mapping_string: ' . ($verification['column_mapping_string'] ?? 'NOT FOUND'));

                // Redirect to show success message and preserve tab
                $current_tab = isset($_POST['current_tab']) ? sanitize_text_field($_POST['current_tab']) : 'sheets-integration';
                $redirect_url = admin_url('admin.php?page=accuflow-extensions&tab=' . $current_tab . '&saved=1');
                wp_redirect($redirect_url);
                exit;
            }

            // Xử lý lưu Liên kết Sản phẩm
            if ( isset( $_POST['accuflow_save_product_links'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'accuflow_save_product_links_nonce' ) ) {
            error_log( 'AccuFlow Debug: Form submission detected - processing product links save' );
                $product_ids = isset( $_POST['product_ids'] ) ? array_map( 'absint', $_POST['product_ids'] ) : [];

                // Save test settings for each product (moved from settings section)
                $test_settings = $current_settings['test_settings'] ?? [];
                foreach ( $_POST as $key => $value ) {
                    if ( strpos( $key, 'test_col_' ) === 0 ) {
                        $product_id = str_replace( 'test_col_', '', $key );
                        $test_settings[$product_id]['test_col'] = absint( $value );
                        error_log( 'AccuFlow Debug: Saving test_col for product ' . $product_id . ': ' . $value );
                    }
                    if ( strpos( $key, 'test_status_' ) === 0 ) {
                        $product_id = str_replace( 'test_status_', '', $key );
                        $test_settings[$product_id]['test_status'] = sanitize_text_field( $value );
                        error_log( 'AccuFlow Debug: Saving test_status for product ' . $product_id . ': ' . $value );
                    }
                }
                error_log( 'AccuFlow Debug: Final test_settings to save: ' . print_r( $test_settings, true ) );

                // Update test settings in config
                $updated_settings = $current_settings;
                $updated_settings['test_settings'] = $test_settings;
                $this->config->save_settings( $updated_settings );

                foreach ( $product_ids as $post_id ) {
                    $fulfillment_method = sanitize_text_field( $_POST['fulfillment_method'][ $post_id ] ?? 'none' );
                    update_post_meta( $post_id, $current_settings['product_fulfillment_method_meta_key'], $fulfillment_method );
                    
                    // Removed duration_days functionality

                    // Reset meta keys for other methods to clear old data
                    delete_post_meta( $post_id, $current_settings['product_sheet_tab_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_endpoint_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_input_fields_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_custom_link_regex_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_json_filter_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_note_filter_meta_key'] );
                    delete_post_meta( $post_id, $current_settings['product_api_show_frontend_meta_key'] );

                    if ( $fulfillment_method === 'google_sheet' ) {
                        $current_tab_gid = isset($_POST['tab_gid'][$post_id]) ? sanitize_text_field( $_POST['tab_gid'][ $post_id ] ) : '';
                        update_post_meta( $post_id, $current_settings['product_sheet_tab_meta_key'], $current_tab_gid );
                    } elseif ( $fulfillment_method === 'api_call' ) {
                        $api_endpoint_url = isset($_POST['api_endpoint_url'][$post_id]) ? sanitize_text_field( $_POST['api_endpoint_url'][ $post_id ] ) : '';
                        $api_input_fields = isset( $_POST['api_input_fields'][ $post_id ] ) ? array_map( 'sanitize_text_field', $_POST['api_input_fields'][ $post_id ] ) : [];
                        $api_custom_link_regex = isset($_POST['api_custom_link_regex'][$post_id]) ? sanitize_text_field( $_POST['api_custom_link_regex'][ $post_id ] ) : '';
                        $api_json_filter = isset($_POST['api_json_filter'][$post_id]) ? sanitize_textarea_field( $_POST['api_json_filter'][ $post_id ] ) : '';
                        $api_note_filter = isset($_POST['api_note_filter'][$post_id]) ? sanitize_textarea_field( $_POST['api_note_filter'][ $post_id ] ) : '';
                        $api_show_frontend = isset($_POST['api_show_frontend'][$post_id]) ? '1' : '';
                        $api_placeholders = isset($_POST['api_placeholders'][$post_id]) ? array_map('sanitize_text_field', $_POST['api_placeholders'][$post_id]) : [];

                        // Validate API endpoint URL contains required placeholders
                        if (!empty($api_endpoint_url)) {
                            error_log('AccuFlow Debug: Saving API endpoint URL: ' . $api_endpoint_url);

                            // Check if URL contains placeholders
                            if (strpos($api_endpoint_url, '{') === false || strpos($api_endpoint_url, '}') === false) {
                                error_log('AccuFlow Debug: Warning - API URL may be missing placeholders: ' . $api_endpoint_url);
                            }
                        }

                        update_post_meta( $post_id, $current_settings['product_api_endpoint_meta_key'], $api_endpoint_url );
                        update_post_meta( $post_id, $current_settings['product_api_input_fields_meta_key'], $api_input_fields );
                        update_post_meta( $post_id, $current_settings['product_api_custom_link_regex_meta_key'], $api_custom_link_regex );
                        update_post_meta( $post_id, $current_settings['product_api_json_filter_meta_key'], $api_json_filter );
                        update_post_meta( $post_id, $current_settings['product_api_note_filter_meta_key'], $api_note_filter );
                        update_post_meta( $post_id, $current_settings['product_api_show_frontend_meta_key'], $api_show_frontend );
                        update_post_meta( $post_id, $current_settings['product_api_placeholders_meta_key'], $api_placeholders );
                    }
                }
                $message = 'Liên kết sản phẩm đã được lưu thành công!';
                $message_type = 'success';
                
                // Redirect to keep current tab
                $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'product-links';
                $redirect_url = admin_url('admin.php?page=accuflow-extensions&tab=' . $current_tab);
                if (!empty($message)) {
                    $redirect_url .= '&message=' . urlencode($message) . '&message_type=' . $message_type;
                }
                wp_redirect($redirect_url);
                exit;
            }
            
            // Removed WooCommerce email management to avoid conflicts

            // Tải lại cấu hình sau khi có thể đã lưu
            $current_settings = $this->config->get_config();

            $paged = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;
            $s = isset( $_GET['s'] ) ? sanitize_text_field( $_GET['s'] ) : '';
            $posts_per_page = 10;
            $args = ['post_type' => 'product', 'post_status' => 'publish', 'posts_per_page' => $posts_per_page, 'paged' => $paged, 's' => $s];
            $product_query = new WP_Query( $args );
            
            $total_pages = $product_query->max_num_pages;

            ?>
            <div class="wrap accuflow-admin-page">
                <h1><span class="dashicons dashicons-forms"></span> AccuFlow - Quản lý Tài khoản tự động</h1>
                <?php if ( $message ) : ?>
                    <div class="notice notice-<?php echo esc_attr( $message_type ); ?> is-dismissible"><p><?php echo esc_html( $message ); ?></p></div>
                <?php endif; ?>
                <?php
                // Determine current tab from URL parameter
                $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'sheets-integration';
                $valid_tabs = ['sheets-integration', 'product-links', 'quick-payment', 'linked-products'];
                if (!in_array($current_tab, $valid_tabs)) {
                    $current_tab = 'sheets-integration';
                }
                ?>
                <div class="accuflow-tabs">
                    <button class="accuflow-tab-button <?php echo $current_tab === 'sheets-integration' ? 'active' : ''; ?>" data-tab="sheets-integration">Google Sheets Integration</button>
                    <button class="accuflow-tab-button <?php echo $current_tab === 'product-links' ? 'active' : ''; ?>" data-tab="product-links">Liên kết Sản phẩm</button>
                    <button class="accuflow-tab-button <?php echo $current_tab === 'quick-payment' ? 'active' : ''; ?>" data-tab="quick-payment">Quick Payment Buttons</button>
                    <button class="accuflow-tab-button <?php echo $current_tab === 'linked-products' ? 'active' : ''; ?>" data-tab="linked-products">Linked Products Buttons</button>
                </div>
                <div id="accuflow-tab-sheets-integration" class="accuflow-tab-content <?php echo $current_tab === 'sheets-integration' ? 'active' : ''; ?>">
                    <?php include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-settings.php'; ?>
                </div>
                <div id="accuflow-tab-product-links" class="accuflow-tab-content <?php echo $current_tab === 'product-links' ? 'active' : ''; ?>">
                    <?php
                    // Prepare variables for product links display
                    $s = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
                    $paged = isset($_GET['paged']) ? absint($_GET['paged']) : 1;

                    // Query products for display
                    $args = [
                        'post_type' => 'product',
                        'post_status' => 'publish',
                        'posts_per_page' => 20,
                        'paged' => $paged,
                    ];

                    if (!empty($s)) {
                        $args['s'] = $s;
                    }

                    $product_query = new WP_Query($args);
                    $total_pages = $product_query->max_num_pages;

                    // Pass instances to the included file
                    $config_instance = $this->config;
                    $admin_instance = $this;

                    include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-product-links.php';
                    ?>
                </div>
                <div id="accuflow-tab-quick-payment" class="accuflow-tab-content <?php echo $current_tab === 'quick-payment' ? 'active' : ''; ?>">
                    <?php include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-quick-payment.php'; ?>
                </div>
                <div id="accuflow-tab-linked-products" class="accuflow-tab-content <?php echo $current_tab === 'linked-products' ? 'active' : ''; ?>">
                    <?php include ACCUFLOW_PLUGIN_DIR . 'admin/partials/admin-display-linked-products.php'; ?>
                </div>
            </div>
            <?php
        }

        public function render_product_list_with_variations( $product_query ) {
            if ( $product_query->have_posts() ) {
                while ( $product_query->have_posts() ) {
                    $product_query->the_post();
                    $product = wc_get_product( get_the_ID() );
                    if ( ! $product ) continue;
                    
                    $this->render_single_product_row( $product );

                    if ( $product->is_type('variable') ) {
                        $variations = $product->get_children();
                        foreach ( $variations as $variation_id ) {
                            $variation_product = wc_get_product( $variation_id );
                            if ( $variation_product && $variation_product->exists() ) {
                                $this->render_single_product_row( $variation_product, true );
                            }
                        }
                    }
                }
                wp_reset_postdata();
            } else {
                echo '<tr><td colspan="6">Không tìm thấy sản phẩm WooCommerce nào.</td></tr>';
            }
        }
        
        private function render_single_product_row( $product, $is_variation = false ) {
            $config = $this->config->get_config();
            $post_id = $product->get_id();

            $current_fulfillment_method = get_post_meta( $post_id, $config['product_fulfillment_method_meta_key'], true ) ?: 'none';

            $current_tab_gid = get_post_meta( $post_id, $config['product_sheet_tab_meta_key'], true );
            $current_api_endpoint_url = get_post_meta( $post_id, $config['product_api_endpoint_meta_key'], true );
            $current_api_input_fields = get_post_meta( $post_id, $config['product_api_input_fields_meta_key'], true );
            if ( ! is_array( $current_api_input_fields ) ) $current_api_input_fields = [];
            $current_api_custom_link_regex = get_post_meta( $post_id, $config['product_api_custom_link_regex_meta_key'], true );
            $current_api_note_filter = get_post_meta( $post_id, $config['product_api_note_filter_meta_key'], true );
            $current_api_show_frontend = get_post_meta( $post_id, $config['product_api_show_frontend_meta_key'], true );

            // Load test settings for this product
            $test_settings = $config['test_settings'] ?? [];
            error_log('AccuFlow Debug: Loading test settings for product ' . $post_id . ': ' . print_r($test_settings, true));
            $current_test_col = $test_settings[$post_id]['test_col'] ?? 4;
            $current_test_status = $test_settings[$post_id]['test_status'] ?? 'Available';
            error_log('AccuFlow Debug: Product ' . $post_id . ' - test_col: ' . $current_test_col . ', test_status: ' . $current_test_status);

            $product_name = $product->get_name();
            if ( $is_variation ) {
                $attribute_summary = wc_get_formatted_variation( $product, true, false, true );
                $product_name = '&nbsp;&nbsp;&nbsp; &rarr; ' . ($attribute_summary ?: 'Biến thể #' . $post_id);
            }
            ?>
            <tr class="<?php echo $is_variation ? 'accuflow-variation-row' : 'accuflow-parent-row'; ?>">
                <td>
                    <input type="hidden" name="product_ids[]" value="<?php echo esc_attr( $post_id ); ?>">
                    <a href="<?php echo esc_url( get_edit_post_link( $post_id ) ); ?>" target="_blank"><?php echo wp_kses_post( $product_name ); ?></a>
                </td>
                <td><?php echo esc_html( $product->get_sku() ); ?></td>
                <td>
                    <select name="fulfillment_method[<?php echo esc_attr( $post_id ); ?>]" class="accuflow-fulfillment-method-select" data-product-id="<?php echo esc_attr( $post_id ); ?>">
                        <option value="none" <?php selected( $current_fulfillment_method, 'none' ); ?>>Không cấu hình</option>
                        <option value="google_sheet" <?php selected( $current_fulfillment_method, 'google_sheet' ); ?>>Google Sheet</option>
                        <option value="api_call" <?php selected( $current_fulfillment_method, 'api_call' ); ?>>API Call</option>
                    </select>
                </td>

                <td>
                    <div class="accuflow-fulfillment-config accuflow-sheet-config" id="sheet-config-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'google_sheet' ) ? 'block' : 'none'; ?>;">
                        <label for="tab_gid_<?php echo esc_attr( $post_id ); ?>">Google Sheet Tab GID:</label>
                        <input type="text" name="tab_gid[<?php echo esc_attr( $post_id ); ?>]" id="tab_gid_<?php echo esc_attr( $post_id ); ?>" value="<?php echo esc_attr( $current_tab_gid ); ?>" class="accuflow-sheets-tab-gid regular-text" placeholder="GID của tab">
                    </div>
                    <div class="accuflow-fulfillment-config accuflow-api-config" id="api-config-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'api_call' ) ? 'block' : 'none'; ?>;">
                        <div style="padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; text-align: center;">
                            <p style="margin: 0 0 15px 0; color: #6c757d;">
                                <strong>🔧 API Call Configuration</strong><br>
                                <small>Click để cấu hình API endpoint, input fields và JSON filter</small>
                            </p>
                            <button type="button" class="button button-secondary accuflow-api-settings-btn" data-product-id="<?php echo esc_attr($post_id); ?>" style="background: #007cba; color: white; border: none; padding: 8px 20px; border-radius: 4px;">
                                ⚙️ Cài đặt API
                            </button>

                            <!-- Hidden inputs to store data -->
                            <input type="hidden" name="api_endpoint_url[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_endpoint_url ); ?>" class="accuflow-api-endpoint-url">
                            <input type="hidden" name="api_json_filter[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( get_post_meta($post_id, $config['product_api_json_filter_meta_key'], true) ); ?>" class="accuflow-api-json-filter">
                            <input type="hidden" name="api_note_filter[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_note_filter ); ?>" class="accuflow-api-note-filter">
                            <input type="hidden" name="api_custom_link_regex[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_custom_link_regex ); ?>" class="accuflow-api-custom-link-regex">
                            <input type="hidden" name="api_show_frontend[<?php echo esc_attr( $post_id ); ?>]" value="<?php echo esc_attr( $current_api_show_frontend ); ?>" class="accuflow-api-show-frontend">

                            <!-- Hidden inputs for input fields -->
                            <?php foreach ($current_api_input_fields as $field): ?>
                            <input type="hidden" name="api_input_fields[<?php echo esc_attr( $post_id ); ?>][]" value="<?php echo esc_attr($field); ?>">
                            <?php endforeach; ?>

                            <!-- Hidden inputs for placeholders -->
                            <?php
                            $current_placeholders = get_post_meta($post_id, $config['product_api_placeholders_meta_key'], true);
                            if (is_array($current_placeholders)):
                                foreach ($current_placeholders as $field => $placeholder):
                            ?>
                            <input type="hidden" name="api_placeholders[<?php echo esc_attr( $post_id ); ?>][<?php echo esc_attr($field); ?>]" value="<?php echo esc_attr($placeholder); ?>">
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </div>




                    </div>
                </td>
                <td>
                    <div class="accuflow-sheet-config-buttons" id="sheet-buttons-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'google_sheet' ) ? 'block' : 'none'; ?>;">
                        <div style="margin-bottom: 8px; display: flex; gap: 8px; flex-wrap: wrap;">
                            <div>
                                <label for="test-col-<?php echo esc_attr($post_id); ?>" style="font-size: 11px; display: block; margin-bottom: 3px;">Số cột test:</label>
                                <input type="number" name="test_col_<?php echo esc_attr($post_id); ?>" id="test-col-<?php echo esc_attr($post_id); ?>" class="accuflow-test-col-number" value="<?php echo esc_attr($current_test_col); ?>" min="0" max="20" style="width: 50px; font-size: 11px; padding: 2px 4px;">
                            </div>
                            <div>
                                <label for="test-status-<?php echo esc_attr($post_id); ?>" style="font-size: 11px; display: block; margin-bottom: 3px;">Tên trạng thái:</label>
                                <input type="text" name="test_status_<?php echo esc_attr($post_id); ?>" id="test-status-<?php echo esc_attr($post_id); ?>" class="accuflow-test-status-name" value="<?php echo esc_attr($current_test_status); ?>" style="width: 80px; font-size: 11px; padding: 2px 4px;">
                            </div>
                        </div>
                        <button type="button" class="button button-small accuflow-sheets-test-link" data-product-id="<?php echo esc_attr( $post_id ); ?>">Test Đọc</button><br>
                        <button type="button" class="button button-small accuflow-sheets-write-log-link" data-product-id="<?php echo esc_attr( $post_id ); ?>" data-product-sku="<?php echo esc_attr( $product->get_sku() ); ?>">Ghi log</button>
                    </div>
                    <div class="accuflow-api-config-buttons" id="api-buttons-<?php echo esc_attr($post_id); ?>" style="display: <?php echo ( $current_fulfillment_method === 'api_call' ) ? 'block' : 'none'; ?>;">
                        <button type="button" class="button button-small accuflow-sheets-test-api-call" data-product-id="<?php echo esc_attr( $post_id ); ?>">Test API</button>
                    </div>
                    <span class="accuflow-sheets-test-result" id="test-result-<?php echo esc_attr( $post_id ); ?>"></span>
                </td>
            </tr>
            <?php
        }

        public function handle_test_connection_ajax() {
            check_ajax_referer( 'accuflow_test_connection_nonce', 'security' );
            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $service = $this->google_api->get_sheets_service();
            if ( $service && ! empty( $spreadsheetId ) ) {
                try {
                    $spreadsheet_metadata = $service->spreadsheets->get( $spreadsheetId );
                    $sheet_title = $spreadsheet_metadata->getProperties()->getTitle();
                    wp_send_json_success( '✅ Kết nối Google Sheet thành công! Tên Sheet: ' . esc_html( $sheet_title ) );
                } catch ( Exception $e ) {
                    wp_send_json_error( '❌ Lỗi kết nối Google Sheet: ' . esc_html( $e->getMessage() ) );
                }
            } else {
                wp_send_json_error( '❌ Không thể khởi tạo dịch vụ Google Sheets. Vui lòng kiểm tra Service Account Credentials và Sheet ID.' );
            }
        }
        
        public function handle_detailed_diagnosis_ajax() {
             check_ajax_referer( 'accuflow_detailed_diagnosis_nonce', 'security' );
            $diagnosis_results = $this->utils->check_php_environment();
            wp_send_json_success( implode( '<br>', $diagnosis_results ) );
        }
        
        public function handle_download_cacert_ajax() {
            check_ajax_referer( 'accuflow_download_cacert_nonce', 'security' );
            if ( $this->utils->download_cacert() ) {
                wp_send_json_success( 'Tệp `cacert.pem` đã được tải xuống và cập nhật thành công!' );
            } else {
                wp_send_json_error( 'Lỗi khi tải xuống hoặc ghi tệp `cacert.pem`.' );
            }
        }

        public function handle_test_product_link_ajax() {
            check_ajax_referer( 'accuflow_test_product_link_nonce', 'security' );
            $product_id = absint( $_POST['product_id'] );
            $tab_gid = sanitize_text_field( $_POST['tab_gid'] );
            $test_col_number = absint( $_POST['test_col_number'] ?? 4 ); // Default to column 4 (Status)
            $test_status_name = sanitize_text_field( $_POST['test_status_name'] ?? 'Available' );

            // Debug: Log what we received
            error_log( 'AccuFlow Test Debug - Product ID: ' . $product_id );
            error_log( 'AccuFlow Test Debug - Received Column: ' . $test_col_number );
            error_log( 'AccuFlow Test Debug - Received Status: ' . $test_status_name );
            error_log( 'AccuFlow Test Debug - POST data: ' . print_r( $_POST, true ) );

            if ( empty( $tab_gid ) ) {
                wp_send_json_error( 'Vui lòng điền GID Tab.' );
            }

            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $sheet_name_from_gid = $this->google_api->get_sheet_name_from_gid( $spreadsheetId, $tab_gid );
            if ( ! $sheet_name_from_gid ) {
                wp_send_json_error( '❌ Không tìm thấy tab với GID "' . esc_html( $tab_gid ) . '".' );
            }

            // Test reading specific column for status matching
            $result = $this->google_api->test_column_status_matching( $tab_gid, $test_col_number, $test_status_name );
            if ( $result ) {
                $found_count = $result['found_count'];
                $total_rows = $result['total_rows'];
                $sample_rows = $result['sample_rows'];

                $message = '✅ Tìm thấy ' . $found_count . ' hàng có trạng thái "' . esc_html( $test_status_name ) . '" trong cột ' . $test_col_number . ' (tổng ' . $total_rows . ' hàng)';
                if ( !empty( $sample_rows ) ) {
                    $message .= '. Ví dụ: hàng ' . implode( ', ', array_slice( $sample_rows, 0, 3 ) );
                }
                wp_send_json_success( $message );
            } else {
                wp_send_json_error( '❌ Không tìm thấy hàng nào có trạng thái "' . esc_html( $test_status_name ) . '" trong cột ' . $test_col_number );
            }
        }

        public function handle_write_connection_log_ajax() {
            check_ajax_referer( 'accuflow_write_connection_log_nonce', 'security' );
            $product_id = absint( $_POST['product_id'] );
            $tab_gid = sanitize_text_field( $_POST['tab_gid'] );
            $product_sku = sanitize_text_field( $_POST['product_sku'] ?? '' );

            $spreadsheetId = $this->config->get_setting( 'spreadsheet_id' );
            $sheet_name_to_log = $this->google_api->get_sheet_name_from_gid( $spreadsheetId, $tab_gid );
            if ( ! $sheet_name_to_log ) {
                wp_send_json_error( '❌ Không tìm thấy tab với GID "' . esc_html( $tab_gid ) . '".' );
            }

            // First, ensure headers are written
            $headers_written = $this->google_api->write_column_headers( $sheet_name_to_log );

            $message_to_log = 'Đã kết nối đúng tab: ' . $sheet_name_to_log . ' (GID: ' . $tab_gid . ')';
            $written = $this->google_api->write_test_log( $sheet_name_to_log, $product_sku, 'Connection Log', 'Success', $message_to_log );

            if ( $written ) {
                $header_msg = $headers_written ? ' (Đã thêm header)' : '';
                wp_send_json_success( '✅ Đã ghi log kết nối thành công vào tab "' . esc_html( $sheet_name_to_log ) . '"' . $header_msg );
            } else {
                wp_send_json_error( '❌ Lỗi khi ghi log kết nối.' );
            }
        }

        public function handle_test_api_call_ajax() {
            check_ajax_referer( 'accuflow_test_api_call_nonce', 'security' );
            $api_endpoint_url = isset($_POST['api_endpoint_url']) ? sanitize_text_field($_POST['api_endpoint_url']) : '';
            $api_json_filter = isset($_POST['api_json_filter']) ? sanitize_textarea_field($_POST['api_json_filter']) : '';
            
            if (empty($api_endpoint_url)) {
                wp_send_json_error('Vui lòng điền API Endpoint URL.');
            }

            // Test với dữ liệu mẫu
            $test_data = [
                'email' => '<EMAIL>',
                'password' => 'testpass123',
                'custom_link' => 'https://example.com',
                'note' => 'Test note'
            ];

            $final_url = $api_endpoint_url;
            foreach ($test_data as $key => $value) {
                $final_url = str_replace('{' . $key . '}', urlencode($value), $final_url);
            }

            $response = wp_remote_get($final_url, array('timeout' => 30));

            if (is_wp_error($response)) {
                wp_send_json_error('❌ Lỗi API: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $http_code = wp_remote_retrieve_response_code($response);
            $json_data = json_decode($body, true);

            if ($http_code == 200) {
                $result_message = "✅ <strong>API Test thành công!</strong><br/>";
                $result_message .= "<strong>HTTP Code:</strong> {$http_code}<br/>";
                
                if ($json_data && !empty($api_json_filter)) {
                    // Use Order Fulfillment class to filter JSON
                    $order_fulfillment = new AccuFlow_Order_Fulfillment($this->config, $this->google_api, $this->utils);
                    $filtered = $order_fulfillment->test_filter_json_response($json_data, $api_json_filter);

                    if ($filtered !== null) {
                        $result_message .= "<strong>🎯 Kết quả lọc:</strong><br/>";
                        if (is_string($filtered)) {
                            $result_message .= "<pre style='background:#e8f5e8;padding:15px;border-radius:5px;border-left:4px solid #28a745;'>" . esc_html($filtered) . "</pre>";
                        } else {
                            $result_message .= "<pre style='background:#e8f5e8;padding:15px;border-radius:5px;'>" . esc_html(print_r($filtered, true)) . "</pre>";
                        }
                    } else {
                        $result_message .= "<strong>⚠️ JSONPath không tìm thấy dữ liệu</strong><br/>";
                    }
                }
                
                $result_message .= "<details style='margin-top:10px;'>";
                $result_message .= "<summary style='cursor:pointer;font-weight:bold;color:#0073aa;'>📄 Xem Response đầy đủ</summary>";
                $result_message .= "<pre style='background:#f8f9fa;padding:15px;border-radius:5px;max-height:300px;overflow-y:auto;margin-top:10px;'>" . htmlspecialchars(json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                $result_message .= "</details>";
                wp_send_json_success($result_message);
            } else {
                wp_send_json_error("❌ <strong>Lỗi HTTP {$http_code}:</strong><br/>" . htmlspecialchars(substr($body, 0, 200)));
            }
        }
        
        // Removed email preview handler to avoid WooCommerce conflicts

        // Removed send test email handler to avoid WooCommerce conflicts

        /**
         * Format kết quả multiple filters cho hiển thị chuyên nghiệp
         */
        private function format_multiple_filter_result($filtered_result, $product_name = '') {
            if (!is_array($filtered_result)) {
                return $filtered_result;
            }
            
            $formatted = [];
            $formatted[] = "📋 Kết quả cho sản phẩm \"" . $product_name . "\":";
            $formatted[] = "";
            
            foreach ($filtered_result as $label => $value) {
                if (is_array($value) || is_object($value)) {
                    // Clean array brackets and format nicely
                    $clean_value = $this->clean_array_brackets($value);
                    $formatted[] = "🔹 " . ucfirst($label) . ": " . $clean_value;
                } else {
                    // Format đặc biệt cho các loại dữ liệu khác nhau
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        // Truncate long URLs to prevent interface breaking
                        $display_url = strlen($value) > 80 ? substr($value, 0, 80) . '...' : $value;
                        $formatted[] = "🔗 " . ucfirst($label) . ": " . $display_url;
                    } elseif (is_numeric($value)) {
                        $formatted[] = "🔢 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'password') !== false || strpos(strtolower($label), 'pass') !== false) {
                        $formatted[] = "🔐 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'username') !== false || strpos(strtolower($label), 'user') !== false) {
                        $formatted[] = "👤 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'email') !== false) {
                        $formatted[] = "📧 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'date') !== false || strpos(strtolower($label), 'time') !== false || strpos(strtolower($label), 'expire') !== false) {
                        $formatted[] = "📅 " . ucfirst($label) . ": " . $value;
                    } else {
                        $formatted[] = "📝 " . ucfirst($label) . ": " . $value;
                    }
                }
            }
            
            $formatted[] = "";
            $formatted[] = "⏰ Cập nhật lúc: " . current_time('H:i \\n\\g\\à\\y d/m/Y');
            
            return implode("\n", $formatted);
        }

        /**
         * Format kết quả cho admin note (hiển thị ngắn gọn)
         */
        private function format_admin_note_result($filtered_result, $product_name, $order_id) {
            if (!is_array($filtered_result)) {
                return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}): " . $filtered_result;
            }
            
            $summary = [];
            foreach ($filtered_result as $label => $value) {
                if (is_string($value) && strlen($value) > 50) {
                    $summary[] = ucfirst($label) . ": " . substr($value, 0, 47) . "...";
                } else {
                    $summary[] = ucfirst($label) . ": " . $value;
                }
            }
            
            return "✅ API thành công cho đơn hàng #{$order_id} ({$product_name}). " . implode(", ", $summary);
        }

        /**
         * Format kết quả cho customer note (hiển thị đầy đủ và đẹp)
         */
        private function format_customer_note_result($filtered_result, $product_name) {
            if (!is_array($filtered_result)) {
                return "Kết quả cho sản phẩm \"{$product_name}\":\n{$filtered_result}";
            }
            
            $formatted = [];
            $formatted[] = "🎯 Thông tin tài khoản cho sản phẩm \"{$product_name}\"";
            $formatted[] = str_repeat("─", 50);
            
            foreach ($filtered_result as $label => $value) {
                if (is_array($value) || is_object($value)) {
                    $formatted[] = "📋 " . ucfirst($label) . ":";
                    $formatted[] = "   " . json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                } else {
                    // Format với icon phù hợp
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        $formatted[] = "🔗 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'password') !== false || strpos(strtolower($label), 'pass') !== false) {
                        $formatted[] = "🔐 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'username') !== false || strpos(strtolower($label), 'user') !== false) {
                        $formatted[] = "👤 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'email') !== false) {
                        $formatted[] = "📧 " . ucfirst($label) . ": " . $value;
                    } elseif (strpos(strtolower($label), 'download') !== false || strpos(strtolower($label), 'link') !== false) {
                        // Truncate long URLs to prevent interface breaking
                        $display_value = strlen($value) > 80 ? substr($value, 0, 80) . '...' : $value;
                        $formatted[] = "⬇️ " . ucfirst($label) . ": " . $display_value;
                    } else {
                        $formatted[] = "📝 " . ucfirst($label) . ": " . $value;
                    }
                }
            }
            
            $formatted[] = str_repeat("─", 50);
            $formatted[] = "⏰ Cập nhật lúc: " . current_time('H:i \\n\\g\\à\\y d/m/Y');
            
            return implode("\n", $formatted);
        }

        /**
         * Clean array brackets and format data nicely
         * Removes [""] brackets and extracts clean content
         */
        private function clean_array_brackets($value) {
            if (is_array($value)) {
                // If it's a simple array with one element, extract it
                if (count($value) === 1 && is_string($value[0])) {
                    return $value[0];
                }
                // For multiple elements, join with commas
                return implode(', ', array_map('strval', $value));
            }

            if (is_object($value)) {
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // If it's a string that looks like ["content"], extract content
            $string_value = (string) $value;
            if (preg_match('/^\["(.+)"\]$/', $string_value, $matches)) {
                return $matches[1];
            }

            return $string_value;
        }

        public function handle_get_product_list_ajax() {
            check_ajax_referer( 'accuflow_get_product_list_nonce', 'security' );

            // Check if WooCommerce is active
            if (!class_exists('WooCommerce')) {
                wp_send_json_error('WooCommerce không được kích hoạt');
                return;
            }

            $search_query = isset($_POST['s']) ? sanitize_text_field($_POST['s']) : '';
            $paged = isset($_POST['paged']) ? absint($_POST['paged']) : 1;

            // Query products
            $args = [
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 20,
                'paged' => $paged,
            ];

            if (!empty($search_query)) {
                $args['s'] = $search_query;
            }

            $product_query = new WP_Query($args);



            // Generate HTML for product rows
            ob_start();
            if ($product_query->have_posts()) {
                while ($product_query->have_posts()) {
                    $product_query->the_post();
                    $product = wc_get_product(get_the_ID());
                    if (!$product) continue;

                    $this->render_single_product_row($product);

                    if ($product->is_type('variable')) {
                        $variations = $product->get_children();
                        foreach ($variations as $variation_id) {
                            $variation_product = wc_get_product($variation_id);
                            if ($variation_product && $variation_product->exists()) {
                                $this->render_single_product_row($variation_product, true);
                            }
                        }
                    }
                }
                wp_reset_postdata();
            } else {
                echo '<tr><td colspan="6">Không tìm thấy sản phẩm WooCommerce nào.</td></tr>';
            }
            $rows_html = ob_get_clean();

            // Generate pagination
            $pagination_html = '';
            if ($product_query->max_num_pages > 1) {
                ob_start();
                $big = 999999999;
                echo paginate_links([
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => $paged,
                    'total' => $product_query->max_num_pages,
                    'prev_text' => '&laquo; Trước',
                    'next_text' => 'Sau &raquo;',
                    'type' => 'list',
                    'add_args' => false,
                    'add_fragment' => '',
                    'before_page_number' => '<span class="accuflow-pagination-link" data-page="',
                    'after_page_number' => '">',
                ]);
                $pagination_html = ob_get_clean();
            }

            wp_send_json_success([
                'rows' => $rows_html,
                'pagination' => $pagination_html
            ]);
        }

        public function handle_auto_save_fulfillment_method_ajax() {
            check_ajax_referer( 'accuflow_save_product_links_nonce', 'security' );

            $product_id = absint( $_POST['product_id'] );
            $fulfillment_method = sanitize_text_field( $_POST['fulfillment_method'] );

            $current_settings = $this->config->get_config();

            if ( $product_id && in_array( $fulfillment_method, ['none', 'google_sheet', 'api_call'] ) ) {
                update_post_meta( $product_id, $current_settings['product_fulfillment_method_meta_key'], $fulfillment_method );
                wp_send_json_success( 'Đã lưu phương thức fulfillment' );
            } else {
                wp_send_json_error( 'Dữ liệu không hợp lệ' );
            }
        }

        /**
         * Debug Tools Page
         */
        public function debug_tools_page() {
            ?>
            <div class="wrap">
                <h1><span class="dashicons dashicons-admin-tools"></span> AccuFlow Debug Tools</h1>

                <div class="notice notice-warning">
                    <p><strong>⚠️ Cảnh báo:</strong> Đây là công cụ debug dành cho developer. Sử dụng cẩn thận!</p>
                </div>

                <div class="postbox-container" style="width: 100%;">
                    <div class="meta-box-sortables">

                        <!-- Set Custom Link for Order -->
                        <div class="postbox">
                            <h2 class="hndle"><span>🔗 Set Custom Link for Order</span></h2>
                            <div class="inside">
                                <p>Set custom_link data cho order cũ không có user input data.</p>
                                <div style="margin: 15px 0;">
                                    <label style="display: block; margin-bottom: 10px;">
                                        <strong>Order ID:</strong><br>
                                        <input type="number" id="set-link-order-id" style="width: 150px;"
                                               placeholder="1327" value="" min="1">
                                    </label>

                                    <label style="display: block; margin-bottom: 10px;">
                                        <strong>Custom Link:</strong><br>
                                        <input type="url" id="order-custom-link" style="width: 100%; max-width: 500px;"
                                               placeholder="https://artlist.io/song/123456/example-song"
                                               value="">
                                    </label>
                                    <p style="color: #666; font-size: 12px;">
                                        Nhập link mà khách hàng muốn process. VD: https://artlist.io/song/123456/example-song
                                    </p>
                                </div>
                                <button type="button" class="button button-secondary" id="set-custom-link-order">
                                    🔗 Set Custom Link
                                </button>
                                <div id="set-custom-link-result" style="margin-top: 15px;"></div>
                            </div>
                        </div>

                        <!-- Reprocess Order -->
                        <div class="postbox">
                            <h2 class="hndle"><span>🔄 Reprocess Order</span></h2>
                            <div class="inside">
                                <p>Chạy lại logic phân phối tài khoản cho đơn hàng.</p>
                                <div style="margin: 15px 0;">
                                    <label style="display: block; margin-bottom: 10px;">
                                        <strong>Order ID:</strong><br>
                                        <input type="number" id="reprocess-order-id" style="width: 150px;"
                                               placeholder="1327" value="" min="1">
                                    </label>
                                </div>
                                <button type="button" class="button button-secondary" id="reprocess-order-btn">
                                    🔄 Reprocess Order
                                </button>
                                <div id="reprocess-order-result" style="margin-top: 15px;"></div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // Debug Order #1327
                $('#debug-order-1327').click(function() {
                    var button = $(this);
                    var resultDiv = $('#debug-order-result');

                    button.prop('disabled', true).text('🔍 Debugging...');
                    resultDiv.html('<div class="notice notice-info"><p>Đang phân tích order #1327...</p></div>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'accuflow_debug_order_ajax',
                            order_id: 1327,
                            nonce: '<?php echo wp_create_nonce('accuflow_debug_order_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                resultDiv.html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                            } else {
                                resultDiv.html('<div class="notice notice-error"><p>Lỗi: ' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            resultDiv.html('<div class="notice notice-error"><p>Lỗi AJAX request</p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('🔍 Debug Order #1327');
                        }
                    });
                });

                // Reprocess Order
                $('#reprocess-order-btn').click(function() {
                    var button = $(this);
                    var resultDiv = $('#reprocess-order-result');
                    var orderId = $('#reprocess-order-id').val();

                    if (!orderId) {
                        resultDiv.html('<div class="notice notice-error"><p>Vui lòng nhập Order ID</p></div>');
                        return;
                    }

                    if (!confirm('Bạn có chắc muốn reprocess order #' + orderId + '?')) {
                        return;
                    }

                    button.prop('disabled', true).text('🔄 Reprocessing...');
                    resultDiv.html('<div class="notice notice-info"><p>Đang reprocess order #' + orderId + '...</p></div>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'accuflow_reprocess_order_ajax',
                            order_id: orderId,
                            nonce: '<?php echo wp_create_nonce('accuflow_reprocess_order_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                resultDiv.html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                            } else {
                                resultDiv.html('<div class="notice notice-error"><p>Lỗi: ' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            resultDiv.html('<div class="notice notice-error"><p>Lỗi AJAX request</p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('🔄 Reprocess Order');
                        }
                    });
                });

                // Set Custom Link for Any Order
                $('#set-custom-link-order').click(function() {
                    var button = $(this);
                    var resultDiv = $('#set-custom-link-result');
                    var orderId = $('#set-link-order-id').val();
                    var customLink = $('#order-custom-link').val();

                    if (!orderId || !customLink) {
                        resultDiv.html('<div class="notice notice-error"><p>Vui lòng nhập Order ID và Custom Link</p></div>');
                        return;
                    }

                    button.prop('disabled', true).text('🔗 Setting...');
                    resultDiv.html('<div class="notice notice-info"><p>Đang set custom link cho order #' + orderId + '...</p></div>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'accuflow_set_custom_link_ajax',
                            order_id: orderId,
                            custom_link: customLink,
                            nonce: '<?php echo wp_create_nonce('accuflow_set_custom_link_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                resultDiv.html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                            } else {
                                resultDiv.html('<div class="notice notice-error"><p>Lỗi: ' + response.data + '</p></div>');
                            }
                        },
                        error: function() {
                            resultDiv.html('<div class="notice notice-error"><p>Lỗi AJAX request</p></div>');
                        },
                        complete: function() {
                            button.prop('disabled', false).text('🔗 Set Custom Link');
                        }
                    });
                });
            });
            </script>

            <style>
            .postbox { margin-bottom: 20px; }
            .postbox h2 { padding: 10px 15px; }
            .postbox .inside { padding: 15px; }
            .button { margin-right: 10px; }
            .notice { padding: 10px; margin: 10px 0; border-left: 4px solid; }
            .notice-success { border-left-color: #46b450; background: #ecf7ed; }
            .notice-error { border-left-color: #dc3232; background: #fbeaea; }
            .notice-info { border-left-color: #00a0d2; background: #e5f5fa; }
            .notice-warning { border-left-color: #ffb900; background: #fff8e5; }
            </style>
            <?php
        }



        /**
         * Handle Reprocess Order AJAX
         */
        public function handle_reprocess_order_ajax() {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'accuflow_reprocess_order_nonce')) {
                wp_send_json_error('Invalid nonce');
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }

            $order_id = intval($_POST['order_id']);
            $order = wc_get_order($order_id);

            if (!$order) {
                wp_send_json_error('Order #' . $order_id . ' not found');
                return;
            }

            try {
                // Add reprocess note
                $order->add_order_note('🔄 Reprocessing order manually via debug tool at ' . current_time('d/m/Y H:i:s'), false);

                // Call fulfillment method
                $this->order_fulfillment->fulfill_order($order_id);

                wp_send_json_success('✅ Order #' . $order_id . ' reprocessed successfully! Check order notes for results.');

            } catch (Exception $e) {
                wp_send_json_error('❌ Error reprocessing order: ' . $e->getMessage());
            }
        }



        /**
         * Handle Set Custom Link AJAX
         */
        public function handle_set_custom_link_ajax() {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'accuflow_set_custom_link_nonce')) {
                wp_send_json_error('Invalid nonce');
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }

            $order_id = intval($_POST['order_id']);
            $custom_link = sanitize_text_field($_POST['custom_link']);

            if (!$order_id || !$custom_link) {
                wp_send_json_error('Missing required data');
                return;
            }

            try {
                $order = wc_get_order($order_id);
                if (!$order) {
                    wp_send_json_error('Order not found');
                    return;
                }

                // Find the GETLINK product item in the order
                $updated = false;
                foreach ($order->get_items() as $item_id => $item) {
                    $product = $item->get_product();
                    if ($product && $product->get_name() === 'GETLINK') {
                        // Set the custom link meta for this order item
                        $item->update_meta_data('_accuflow_user_custom_link_input', $custom_link);
                        $item->save();
                        $updated = true;
                        break;
                    }
                }

                if (!$updated) {
                    wp_send_json_error('GETLINK product not found in order');
                    return;
                }

                // Add order note
                $order->add_order_note('🔗 Custom link set manually via debug tool: ' . $custom_link . ' at ' . current_time('d/m/Y H:i:s'));

                wp_send_json_success('✅ Custom link đã được set thành công cho Order #' . $order_id . '!<br>' .
                                   '🔗 Link: ' . $custom_link . '<br>' .
                                   '📝 Order item meta đã được cập nhật<br>' .
                                   '🔄 Bây giờ có thể reprocess order để test API call');

            } catch (Exception $e) {
                wp_send_json_error('❌ Error setting custom link: ' . $e->getMessage());
            }
        }



        /**
         * Helper method to use order fulfillment's filter_json_response
         */
        private function filter_json_response($json_data, $json_filter) {
            // Use reflection to call private method from order fulfillment class
            $reflection = new ReflectionClass($this->order_fulfillment);
            $method = $reflection->getMethod('filter_json_response');
            $method->setAccessible(true);
            return $method->invoke($this->order_fulfillment, $json_data, $json_filter);
        }

        // Quick Payment Buttons AJAX handlers
        public function handle_qpb_add_to_cart() {
            check_ajax_referer('qpb_nonce', 'nonce');
            $product_id = intval($_POST['product_id']);
            if ($product_id && WC()->cart) {
                WC()->cart->add_to_cart($product_id);
                wp_send_json_success();
            } else {
                wp_send_json_error();
            }
        }

        public function handle_qpb_buy_now() {
            check_ajax_referer('qpb_nonce', 'nonce');
            $product_id = intval($_POST['product_id']);
            if ($product_id && WC()->cart) {
                WC()->cart->empty_cart();
                WC()->cart->add_to_cart($product_id);
                wp_send_json_success(['redirect' => wc_get_checkout_url()]);
            } else {
                wp_send_json_error();
            }
        }
    }
}
