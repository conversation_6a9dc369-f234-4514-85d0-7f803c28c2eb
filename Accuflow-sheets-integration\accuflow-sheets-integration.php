<?php
/**
 * Plugin Name: AccuFlow Extensions
 * Plugin URI:  https://accuflow.com/
 * Description: Bộ plugin tổng hợp bao gồm: Google Sheets Integration, Quick Payment Buttons, và WooCommerce Linked Products Buttons. Tự động phân phối tà<PERSON>, tù<PERSON> chỉnh nút thanh toán và hiển thị sản phẩm liên quan.
 * Version:     3.0.0
 * Author:      AccuFlow Team
 * Author URI:  https://accuflow.com
 * License:     GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: accuflow-extensions
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Định nghĩa các constants
define( 'ACCUFLOW_EXTENSIONS_VERSION', '3.0.0' );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'ACCUFLOW_EXTENSIONS_PLUGIN_FILE', __FILE__ );
define( 'ACCUFLOW_VENDOR_DIR', ACCUFLOW_EXTENSIONS_PLUGIN_DIR . 'vendor/' );

// Backward compatibility constants
define( 'ACCUFLOW_VERSION', ACCUFLOW_EXTENSIONS_VERSION );
define( 'ACCUFLOW_PLUGIN_DIR', ACCUFLOW_EXTENSIONS_PLUGIN_DIR );
define( 'ACCUFLOW_PLUGIN_URL', ACCUFLOW_EXTENSIONS_PLUGIN_URL );

/**
 * Kiểm tra các yêu cầu hệ thống
 */
function accuflow_extensions_check_requirements() {
    $errors = array();
    
    // Kiểm tra PHP version
    if ( version_compare( PHP_VERSION, '7.4', '<' ) ) {
        $errors[] = sprintf( 'AccuFlow Extensions requires PHP 7.4 or higher. You are running PHP %s.', PHP_VERSION );
    }
    
    // Kiểm tra WordPress version
    if ( version_compare( get_bloginfo( 'version' ), '5.0', '<' ) ) {
        $errors[] = sprintf( 'AccuFlow Extensions requires WordPress 5.0 or higher. You are running WordPress %s.', get_bloginfo( 'version' ) );
    }
    
    // Kiểm tra WooCommerce
    if ( ! class_exists( 'WooCommerce' ) ) {
        $errors[] = 'AccuFlow Extensions requires WooCommerce to be installed and activated.';
    }
    
    // Kiểm tra Google API Client
    if ( ! file_exists( ACCUFLOW_VENDOR_DIR . 'autoload.php' ) ) {
        $errors[] = 'AccuFlow Extensions: Google API Client library not found. Please run "composer install" in the plugin directory.';
    }
    
    if ( ! empty( $errors ) ) {
        add_action( 'admin_notices', function() use ( $errors ) {
            foreach ( $errors as $error ) {
                echo '<div class="notice notice-error"><p>' . esc_html( $error ) . '</p></div>';
            }
        });
        return false;
    }
    
    return true;
}

/**
 * Load plugin files
 */
function accuflow_extensions_load_files() {
    // Load Google API Client
    require_once ACCUFLOW_VENDOR_DIR . 'autoload.php';
    
    // Load core classes
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-config.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-utils.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-google-api.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-email-template.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-accuflow-order-fulfillment.php';
    
    // Load extension classes
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-quick-payment-buttons.php';
    require_once ACCUFLOW_PLUGIN_DIR . 'includes/class-linked-products-buttons.php';
    
    // Load admin class
    if ( is_admin() ) {
        require_once ACCUFLOW_PLUGIN_DIR . 'admin/class-accuflow-admin.php';
    }
    
    // Load debug files in debug mode
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        if ( file_exists( ACCUFLOW_PLUGIN_DIR . 'test-plugin-integration.php' ) ) {
            require_once ACCUFLOW_PLUGIN_DIR . 'test-plugin-integration.php';
        }
        if ( file_exists( ACCUFLOW_PLUGIN_DIR . 'debug-quick-payment.php' ) ) {
            require_once ACCUFLOW_PLUGIN_DIR . 'debug-quick-payment.php';
        }
    }
}

/**
 * Initialize plugin
 */
function accuflow_extensions_init() {
    // Kiểm tra yêu cầu hệ thống
    if ( ! accuflow_extensions_check_requirements() ) {
        return;
    }
    
    // Load files
    accuflow_extensions_load_files();
    
    // Initialize core components
    $config = new AccuFlow_Config();
    $utils = new AccuFlow_Utils( $config );
    $google_api = new AccuFlow_Google_API( $config, $utils );
    $order_fulfillment = new AccuFlow_Order_Fulfillment( $config, $google_api );
    $order_fulfillment->init();
    
    // Initialize Quick Payment Buttons
    if ( class_exists( 'Quick_Payment_Buttons' ) ) {
        $quick_payment = new Quick_Payment_Buttons();
        $quick_payment->init();
    }
    
    // Initialize Linked Products Buttons
    if ( class_exists( 'Linked_Products_Buttons' ) ) {
        $linked_products = new Linked_Products_Buttons();
        $linked_products->init();
    }
    
    // Initialize admin
    if ( is_admin() && class_exists( 'AccuFlow_Admin' ) ) {
        $admin = new AccuFlow_Admin( $config, $google_api, $utils, $order_fulfillment );
        $admin->init();
        
        // Set global variable for use in included files
        global $accuflow_admin;
        $accuflow_admin = $admin;
    }
}

/**
 * Plugin activation hook
 */
function accuflow_extensions_activate() {
    // Kiểm tra yêu cầu hệ thống khi activate
    if ( ! accuflow_extensions_check_requirements() ) {
        wp_die( 'AccuFlow Extensions cannot be activated due to system requirements not being met.' );
    }
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Plugin deactivation hook
 */
function accuflow_extensions_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Hook vào WordPress
add_action( 'plugins_loaded', 'accuflow_extensions_init' );
register_activation_hook( __FILE__, 'accuflow_extensions_activate' );
register_deactivation_hook( __FILE__, 'accuflow_extensions_deactivate' );

// Backward compatibility function
function accuflow_run() {
    accuflow_extensions_init();
}

/**
 * Add plugin action links
 */
function accuflow_extensions_plugin_action_links( $links ) {
    $settings_link = '<a href="' . admin_url( 'admin.php?page=accuflow-extensions' ) . '">' . __( 'Settings', 'accuflow-extensions' ) . '</a>';
    array_unshift( $links, $settings_link );
    return $links;
}
add_filter( 'plugin_action_links_' . plugin_basename( __FILE__ ), 'accuflow_extensions_plugin_action_links' );

/**
 * Add plugin meta links
 */
function accuflow_extensions_plugin_row_meta( $links, $file ) {
    if ( plugin_basename( __FILE__ ) === $file ) {
        $row_meta = array(
            'docs'    => '<a href="https://accuflow.com/docs/" target="_blank">' . __( 'Documentation', 'accuflow-extensions' ) . '</a>',
            'support' => '<a href="https://accuflow.com/support/" target="_blank">' . __( 'Support', 'accuflow-extensions' ) . '</a>',
        );
        return array_merge( $links, $row_meta );
    }
    return $links;
}
add_filter( 'plugin_row_meta', 'accuflow_extensions_plugin_row_meta', 10, 2 );

/**
 * Load text domain for translations
 */
function accuflow_extensions_load_textdomain() {
    load_plugin_textdomain( 'accuflow-extensions', false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );
}
add_action( 'plugins_loaded', 'accuflow_extensions_load_textdomain' );

/**
 * Check if WooCommerce is active
 */
function accuflow_extensions_is_woocommerce_active() {
    return class_exists( 'WooCommerce' );
}

/**
 * Display admin notice if WooCommerce is not active
 */
function accuflow_extensions_woocommerce_missing_notice() {
    if ( ! accuflow_extensions_is_woocommerce_active() ) {
        echo '<div class="notice notice-error"><p>';
        echo '<strong>AccuFlow Extensions</strong> requires WooCommerce to be installed and activated.';
        echo '</p></div>';
    }
}
add_action( 'admin_notices', 'accuflow_extensions_woocommerce_missing_notice' );
