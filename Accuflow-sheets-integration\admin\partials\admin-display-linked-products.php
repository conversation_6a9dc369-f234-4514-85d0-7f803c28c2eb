<?php
/**
 * Linked Products Buttons Admin Display
 *
 * @package AccuFlow_Extensions
 * @subpackage Admin
 * @since 3.0.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Xử lý lưu settings
if (isset($_POST['save_linked_products_settings'])) {
    check_admin_referer('linked_products_settings_nonce');

    $lpb_enabled = isset($_POST['lpb_enabled']) ? 1 : 0;
    $custom_css = isset($_POST['choacc_linked_buttons_custom_css']) ? wp_unslash($_POST['choacc_linked_buttons_custom_css']) : '';
    $group_label = isset($_POST['choacc_linked_buttons_group_label']) ? sanitize_text_field($_POST['choacc_linked_buttons_group_label']) : 'Chọn gói sản phẩm';

    update_option('lpb_enabled', $lpb_enabled);
    update_option('choacc_linked_buttons_custom_css', $custom_css);
    update_option('choacc_linked_buttons_group_label', $group_label);

    echo '<div class="notice notice-success is-dismissible"><p>Cài đặt Linked Products Buttons đã được lưu thành công!</p></div>';
}

$lpb_enabled = get_option('lpb_enabled', 1); // Mặc định bật
$current_css = get_option('choacc_linked_buttons_custom_css', '');
$group_label = get_option('choacc_linked_buttons_group_label', 'Chọn gói sản phẩm');
?>

<div class="wrap">
    <h2>Linked Products Buttons Settings</h2>
    <p>Hiển thị sản phẩm bán chéo (cross-sell) dưới dạng nút bấm đơn giản trên trang sản phẩm.</p>
    
    <form method="post" action="">
        <?php wp_nonce_field('linked_products_settings_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="lpb_enabled">Bật/Tắt Linked Products Buttons</label>
                </th>
                <td>
                    <label>
                        <input type="checkbox" id="lpb_enabled" name="lpb_enabled" value="1" <?php checked($lpb_enabled, 1); ?> />
                        Bật Linked Products Buttons
                    </label>
                    <p class="description">Bật/tắt chức năng hiển thị nút sản phẩm liên quan trên trang sản phẩm.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="choacc_linked_buttons_group_label">Tiêu đề nhóm nút</label>
                </th>
                <td>
                    <input type="text" name="choacc_linked_buttons_group_label" id="choacc_linked_buttons_group_label" value="<?php echo esc_attr($group_label); ?>" class="regular-text" />
                    <p class="description">
                        Tiêu đề hiển thị phía trên các nút sản phẩm liên quan.
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="choacc_linked_buttons_custom_css">CSS Tùy chỉnh</label>
                </th>
                <td>
                    <textarea name="choacc_linked_buttons_custom_css" id="choacc_linked_buttons_custom_css" rows="15" cols="80" class="large-text code"><?php echo esc_textarea($current_css); ?></textarea>
                    <p class="description">
                        Nhập CSS tùy chỉnh để thay đổi giao diện của các nút. Để trống để sử dụng style mặc định.
                    </p>
                </td>
            </tr>
        </table>
        
        <?php submit_button('Lưu cài đặt', 'primary', 'save_linked_products_settings'); ?>
    </form>
    
    <hr>
    
    <h3>CSS Mặc định</h3>
    <p>Dưới đây là CSS mặc định được sử dụng cho các nút:</p>
    <pre style="background: #f1f1f1; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>.linked-products-group {
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}
.linked-products-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}
.linked-product-btn {
    display: inline-block;
    padding: 6px 14px;
    font-size: 13px;
    background: #f5f5f5;
    color: #333;
    text-decoration: none;
    border-radius: 5px;
    border: 1px solid #ccc;
    transition: 0.3s ease;
    white-space: nowrap;
}
.linked-product-btn:hover {
    background-color: #2563eb;
    color: #fff;
    border-color: #2563eb;
}</code></pre>
    
    <h3>Hướng dẫn sử dụng</h3>
    <ol>
        <li><strong>Tạo tên nút tùy chỉnh:</strong> Vào trang chỉnh sửa sản phẩm → điền vào trường "Tên nút liên kết" (nếu để trống sẽ dùng tên sản phẩm)</li>
        <li><strong>Thiết lập sản phẩm bán chéo:</strong> Trong sản phẩm chính → Thêm các sản phẩm vào mục "Bán chéo" (Cross-sells)</li>
        <li><strong>Tùy chỉnh giao diện:</strong> Có thể đổi tiêu đề nhóm và CSS ngay tại đây</li>
        <li><strong>Hiển thị:</strong> Các nút sẽ xuất hiện sau nút "Thêm vào giỏ hàng" trên trang sản phẩm</li>
    </ol>
    
    <div class="notice notice-info">
        <p><strong>Lưu ý:</strong> Chỉ những sản phẩm có thiết lập "Bán chéo" mới hiển thị các nút liên kết. Đảm bảo bạn đã thêm sản phẩm vào mục Cross-sells trong WooCommerce.</p>
    </div>
</div>
