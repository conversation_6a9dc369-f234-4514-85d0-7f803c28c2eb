<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Google_AccessToken_Revoke' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_AccessToken_Verify' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_AuthHandler_AuthHandlerFactory' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_AuthHandler_Guzzle6AuthHandler' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_AuthHandler_Guzzle7AuthHandler' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Client' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Collection' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Exception' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Http_Batch' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Http_MediaFileUpload' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Http_REST' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Model' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Service' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Service_Exception' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Service_Resource' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Task_Composer' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Task_Exception' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Task_Retryable' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Task_Runner' => $vendorDir . '/google/apiclient/src/aliases.php',
    'Google_Utils_UriTemplate' => $vendorDir . '/google/apiclient/src/aliases.php',
);
