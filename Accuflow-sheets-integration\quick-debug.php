<?php
/**
 * Quick Debug - Thêm code này vào functions.php của theme để debug nhanh
 */

// Hiển thị thông tin debug ở footer cho admin
add_action('wp_footer', function() {
    if (!current_user_can('manage_options')) return;
    
    echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #fff; border: 2px solid #0073aa; padding: 15px; border-radius: 5px; z-index: 9999; max-width: 300px; font-size: 12px;">';
    echo '<h4 style="margin: 0 0 10px 0; color: #0073aa;">🔍 AccuFlow Debug</h4>';
    
    // 1. Plugin active
    echo '<p><strong>Plugin:</strong> ';
    if (defined('ACCUFLOW_VERSION')) {
        echo '<span style="color: green;">✅ Active (v' . ACCUFLOW_VERSION . ')</span>';
    } else {
        echo '<span style="color: red;">❌ Not loaded</span>';
    }
    echo '</p>';
    
    // 2. Classes loaded
    echo '<p><strong>Classes:</strong><br>';
    echo '&nbsp;&nbsp;QPB: ' . (class_exists('Quick_Payment_Buttons') ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
    echo '&nbsp;&nbsp;LPB: ' . (class_exists('Linked_Products_Buttons') ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
    echo '</p>';
    
    // 3. WooCommerce
    echo '<p><strong>WooCommerce:</strong> ';
    if (class_exists('WooCommerce')) {
        echo '<span style="color: green;">✅ Active</span>';
    } else {
        echo '<span style="color: red;">❌ Not active</span>';
    }
    echo '</p>';
    
    // 4. Current page
    echo '<p><strong>Page:</strong> ';
    if (is_product()) {
        echo '<span style="color: green;">✅ Product page</span>';
        global $product;
        if ($product) {
            echo '<br>&nbsp;&nbsp;ID: ' . $product->get_id();
            echo '<br>&nbsp;&nbsp;Purchasable: ' . ($product->is_purchasable() ? 'YES' : 'NO');
        }
    } else {
        echo '<span style="color: orange;">⚠️ Not product page</span>';
    }
    echo '</p>';
    
    // 5. Assets
    if (defined('ACCUFLOW_PLUGIN_DIR')) {
        $css_exists = file_exists(ACCUFLOW_PLUGIN_DIR . 'assets/css/quick-payment-buttons.css');
        $js_exists = file_exists(ACCUFLOW_PLUGIN_DIR . 'assets/js/quick-payment-buttons.js');
        
        echo '<p><strong>Assets:</strong><br>';
        echo '&nbsp;&nbsp;CSS: ' . ($css_exists ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
        echo '&nbsp;&nbsp;JS: ' . ($js_exists ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
        echo '</p>';
    }
    
    echo '<p style="margin: 10px 0 0 0; font-size: 10px; color: #666;">Refresh trang để update info</p>';
    echo '</div>';
});

// Force hiển thị nút test (chỉ cho admin)
add_action('woocommerce_single_product_summary', function() {
    if (!current_user_can('manage_options')) return;
    
    global $product;
    if (!$product) return;
    
    echo '<div style="background: #f0f8ff; border: 1px solid #0073aa; padding: 10px; margin: 10px 0; border-radius: 3px;">';
    echo '<h4 style="margin: 0 0 5px 0; color: #0073aa;">🧪 Test Quick Payment Buttons</h4>';
    echo '<div class="quick-payment-buttons" data-product-id="' . esc_attr($product->get_id()) . '">';
    echo '<button class="quick-payment-buy-button" style="background: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 3px; margin-right: 5px; cursor: pointer;">Mua ngay</button>';
    echo '<button class="quick-payment-addtocart-button" style="background: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer;">Thêm vào giỏ</button>';
    echo '</div>';
    echo '<p style="margin: 5px 0 0 0; font-size: 11px; color: #666;">Nếu thấy nút này → plugin đã load. Nếu click không hoạt động → lỗi JS/AJAX</p>';
    echo '</div>';
}, 25);
?>
