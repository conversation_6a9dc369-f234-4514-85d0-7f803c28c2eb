<?php
/**
 * Debug file for Quick Payment Buttons
 * Add this to your theme's functions.php temporarily to debug
 */

// Ngăn chặn truy cập trực tiếp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Debug function to check Quick Payment Buttons
function debug_quick_payment_buttons() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    echo '<div style="background: #fff; border: 1px solid #ccc; padding: 20px; margin: 20px; border-radius: 5px;">';
    echo '<h3>🔍 Debug Quick Payment Buttons</h3>';
    
    // Check if class exists
    echo '<p><strong>1. Class exists:</strong> ';
    if (class_exists('Quick_Payment_Buttons')) {
        echo '<span style="color: green;">✅ YES</span>';
    } else {
        echo '<span style="color: red;">❌ NO</span>';
    }
    echo '</p>';
    
    // Check if WooCommerce is active
    echo '<p><strong>2. WooCommerce active:</strong> ';
    if (class_exists('WooCommerce')) {
        echo '<span style="color: green;">✅ YES</span>';
    } else {
        echo '<span style="color: red;">❌ NO</span>';
    }
    echo '</p>';
    
    // Check if we're on a product page
    echo '<p><strong>3. Current page:</strong> ';
    if (is_product()) {
        echo '<span style="color: green;">✅ Product page</span>';
        
        global $product;
        if ($product) {
            echo '<br>Product ID: ' . $product->get_id();
            echo '<br>Product purchasable: ' . ($product->is_purchasable() ? 'YES' : 'NO');
        }
    } else {
        echo '<span style="color: orange;">⚠️ Not a product page</span>';
    }
    echo '</p>';
    
    // Check if hooks are registered
    echo '<p><strong>4. Hooks registered:</strong><br>';
    
    global $wp_filter;
    
    // Check wp_enqueue_scripts hook
    if (isset($wp_filter['wp_enqueue_scripts'])) {
        $found_enqueue = false;
        foreach ($wp_filter['wp_enqueue_scripts']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Quick_Payment_Buttons' &&
                    $callback['function'][1] === 'enqueue_scripts') {
                    $found_enqueue = true;
                    break 2;
                }
            }
        }
        echo '&nbsp;&nbsp;wp_enqueue_scripts: ' . ($found_enqueue ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
    }
    
    // Check wp_head hook
    if (isset($wp_filter['wp_head'])) {
        $found_head = false;
        foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Quick_Payment_Buttons' &&
                    $callback['function'][1] === 'inject_custom_css') {
                    $found_head = true;
                    break 2;
                }
            }
        }
        echo '&nbsp;&nbsp;wp_head: ' . ($found_head ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>') . '<br>';
    }
    
    // Check woocommerce_single_product_summary hook
    if (isset($wp_filter['woocommerce_single_product_summary'])) {
        $found_summary = false;
        foreach ($wp_filter['woocommerce_single_product_summary']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'Quick_Payment_Buttons' &&
                    $callback['function'][1] === 'display_quick_payment_buttons') {
                    $found_summary = true;
                    echo '&nbsp;&nbsp;woocommerce_single_product_summary (priority ' . $priority . '): <span style="color: green;">✅</span><br>';
                    break 2;
                }
            }
        }
        if (!$found_summary) {
            echo '&nbsp;&nbsp;woocommerce_single_product_summary: <span style="color: red;">❌</span><br>';
        }
    }
    echo '</p>';
    
    // Check if CSS/JS files exist
    echo '<p><strong>5. Asset files:</strong><br>';
    $css_path = ACCUFLOW_PLUGIN_DIR . 'assets/css/quick-payment-buttons.css';
    $js_path = ACCUFLOW_PLUGIN_DIR . 'assets/js/quick-payment-buttons.js';
    
    echo '&nbsp;&nbsp;CSS file: ' . (file_exists($css_path) ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . '<br>';
    echo '&nbsp;&nbsp;JS file: ' . (file_exists($js_path) ? '<span style="color: green;">✅ EXISTS</span>' : '<span style="color: red;">❌ MISSING</span>') . '<br>';
    echo '</p>';
    
    // Check settings
    echo '<p><strong>6. Settings:</strong><br>';
    $custom_css = get_option('qpb_custom_css', '');
    echo '&nbsp;&nbsp;Custom CSS: ' . (empty($custom_css) ? 'Empty' : 'Has content (' . strlen($custom_css) . ' chars)') . '<br>';
    echo '</p>';
    
    echo '</div>';
}

// Add debug info to product pages for admins
add_action('woocommerce_single_product_summary', 'debug_quick_payment_buttons', 5);

// Also add to admin bar for easy access
add_action('admin_bar_menu', function($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $wp_admin_bar->add_node([
        'id'    => 'debug-qpb',
        'title' => '🔍 Debug QPB',
        'href'  => '#',
        'meta'  => [
            'onclick' => 'jQuery("body").prepend(jQuery("#debug-qpb-info").toggle()); return false;'
        ]
    ]);
}, 100);

// Add debug info to footer
add_action('wp_footer', function() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    echo '<div id="debug-qpb-info" style="display: none; position: fixed; top: 50px; right: 20px; z-index: 99999; max-width: 400px;">';
    debug_quick_payment_buttons();
    echo '</div>';
});
