<?php
/**
 * Test file for AccuFlow Extensions
 * 
 * This file can be used to test basic functionality of the integrated plugin
 * Run this file in WordPress admin or via WP-CLI to verify integration
 */

// Ngăn chặn truy cập trực tiếp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

function accuflow_extensions_test() {
    $results = [];
    
    // Test 1: Check if main plugin file exists and is loaded
    $results['main_plugin'] = [
        'test' => 'Main plugin file loaded',
        'status' => defined('ACCUFLOW_VERSION') ? 'PASS' : 'FAIL',
        'details' => defined('ACCUFLOW_VERSION') ? 'Version: ' . ACCUFLOW_VERSION : 'Plugin not loaded'
    ];
    
    // Test 2: Check if Quick Payment Buttons class exists
    $results['quick_payment_class'] = [
        'test' => 'Quick Payment Buttons class exists',
        'status' => class_exists('Quick_Payment_Buttons') ? 'PASS' : 'FAIL',
        'details' => class_exists('Quick_Payment_Buttons') ? 'Class loaded successfully' : 'Class not found'
    ];
    
    // Test 3: Check if Linked Products Buttons class exists
    $results['linked_products_class'] = [
        'test' => 'Linked Products Buttons class exists',
        'status' => class_exists('Linked_Products_Buttons') ? 'PASS' : 'FAIL',
        'details' => class_exists('Linked_Products_Buttons') ? 'Class loaded successfully' : 'Class not found'
    ];
    
    // Test 4: Check if AccuFlow Config class exists
    $results['accuflow_config'] = [
        'test' => 'AccuFlow Config class exists',
        'status' => class_exists('AccuFlow_Config') ? 'PASS' : 'FAIL',
        'details' => class_exists('AccuFlow_Config') ? 'Class loaded successfully' : 'Class not found'
    ];
    
    // Test 5: Check if admin menu is registered
    global $submenu;
    $menu_exists = false;
    if (isset($submenu['woocommerce'])) {
        foreach ($submenu['woocommerce'] as $item) {
            if (isset($item[2]) && $item[2] === 'accuflow-extensions') {
                $menu_exists = true;
                break;
            }
        }
    }
    
    $results['admin_menu'] = [
        'test' => 'Admin menu registered',
        'status' => $menu_exists ? 'PASS' : 'FAIL',
        'details' => $menu_exists ? 'Menu found in WooCommerce submenu' : 'Menu not found'
    ];
    
    // Test 6: Check if assets exist
    $css_exists = file_exists(ACCUFLOW_PLUGIN_DIR . 'assets/css/quick-payment-buttons.css');
    $js_exists = file_exists(ACCUFLOW_PLUGIN_DIR . 'assets/js/quick-payment-buttons.js');
    
    $results['assets'] = [
        'test' => 'Asset files exist',
        'status' => ($css_exists && $js_exists) ? 'PASS' : 'FAIL',
        'details' => sprintf('CSS: %s, JS: %s', 
            $css_exists ? 'Found' : 'Missing',
            $js_exists ? 'Found' : 'Missing'
        )
    ];
    
    // Test 7: Check if WooCommerce is active
    $wc_active = class_exists('WooCommerce');
    $results['woocommerce'] = [
        'test' => 'WooCommerce compatibility',
        'status' => $wc_active ? 'PASS' : 'WARNING',
        'details' => $wc_active ? 'WooCommerce is active' : 'WooCommerce not detected'
    ];
    
    return $results;
}

// Function to display test results
function accuflow_extensions_display_test_results() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized access');
    }
    
    $results = accuflow_extensions_test();
    
    echo '<div class="wrap">';
    echo '<h1>AccuFlow Extensions - Test Results</h1>';
    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead><tr><th>Test</th><th>Status</th><th>Details</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($results as $key => $result) {
        $status_class = '';
        switch ($result['status']) {
            case 'PASS':
                $status_class = 'style="color: green; font-weight: bold;"';
                break;
            case 'FAIL':
                $status_class = 'style="color: red; font-weight: bold;"';
                break;
            case 'WARNING':
                $status_class = 'style="color: orange; font-weight: bold;"';
                break;
        }
        
        echo sprintf(
            '<tr><td>%s</td><td %s>%s</td><td>%s</td></tr>',
            esc_html($result['test']),
            $status_class,
            esc_html($result['status']),
            esc_html($result['details'])
        );
    }
    
    echo '</tbody></table>';
    echo '</div>';
}

// Add test page to admin menu (only for testing)
if (defined('WP_DEBUG') && WP_DEBUG) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'tools.php',
            'AccuFlow Extensions Test',
            'AccuFlow Test',
            'manage_options',
            'accuflow-extensions-test',
            'accuflow_extensions_display_test_results'
        );
    });
}
