document.addEventListener("DOMContentLoaded", function () {
    const emailInput = document.querySelector("#accuflow_user_email_input");
    const passwordInput = document.querySelector("#accuflow_user_password_input");
    const linkInput = document.querySelector("#accuflow_user_custom_link_input");
    const noteInput = document.querySelector("#accuflow_user_note_input");

    // Add preview result container
    const previewBox = document.createElement("div");
    previewBox.id = "accuflow-preview-result";
    previewBox.style.marginTop = "1em";
    previewBox.style.border = "1px solid #ccc";
    previewBox.style.padding = "1em";
    previewBox.style.display = "none";

    document.querySelector(".accuflow-api-inputs")?.appendChild(previewBox);

    // Trigger preview when any field changes
    [emailInput, passwordInput, linkInput, noteInput].forEach((field) => {
        if (field) {
            field.addEventListener("change", triggerPreview);
        }
    });

    async function triggerPreview() {
        const email = emailInput?.value || "";
        const password = passwordInput?.value || "";
        const link = linkInput?.value || "";
        const note = noteInput?.value || "";

        previewBox.style.display = "block";
        previewBox.innerHTML = "⏳ Đang kiểm tra dữ liệu...";

        try {
            const response = await fetch("/wp-json/accuflow/v1/preview-account", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ email, password, custom_link: link, note }),
            });

            const result = await response.json();

            if (result.success) {
                previewBox.innerHTML = `
                    ✅ <strong>Xem trước thành công:</strong><br/>
                    <strong>Username:</strong> ${result.data.username}<br/>
                    <strong>Password:</strong> ${result.data.password}<br/>
                    <strong>Login URL:</strong> <a href="${result.data.login_url}" target="_blank">${result.data.login_url}</a>
                `;
            } else {
                previewBox.innerHTML = `❌ <strong>Lỗi:</strong> ${result.message || "Không rõ nguyên nhân"}`;
            }
        } catch (err) {
            previewBox.innerHTML = `❌ <strong>Lỗi mạng hoặc máy chủ</strong>: ${err.message}`;
        }
    }
});
