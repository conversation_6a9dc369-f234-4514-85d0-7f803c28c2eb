<?php
/**
 * AccuFlow - Google Sheets Integration: Partial cho hiển thị cài đặt chung.
 *
 * @package AccuFlow
 * @subpackage Admin/Partials
 * @since 2.5.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<form method="post" action="?page=accuflow-sheets-integration&tab=settings">
    <?php wp_nonce_field( 'accuflow_save_settings_nonce' ); ?>
    <input type="hidden" name="current_tab" value="settings">
    <table class="form-table">
        <tbody>
            <tr>
                <th scope="row"><label for="spreadsheet_id">Google Sheet ID</label></th>
                <td>
                    <input name="spreadsheet_id" type="text" id="spreadsheet_id" value="<?php echo esc_attr( $current_settings['spreadsheet_id'] ); ?>" class="regular-text" placeholder="ID của Google Sheet của bạn">
                    <p class="description">Bạn có thể tìm thấy ID này trong URL của Google Sheet (ví dụ: <code>https://docs.google.com/spreadsheets/d/<span style="color:red;">YOUR_SHEET_ID</span>/edit</code>)</p>
                </td>
            </tr>
            <tr>
                <th scope="row" colspan="2">
                    <h3 style="margin: 0; color: #0073aa;">🔧 Service Account Credentials</h3>
                    <p style="font-weight: normal; color: #666;">Nhập từng trường riêng biệt từ file JSON để tránh lỗi format</p>
                </th>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_project_id">Project ID</label></th>
                <td><input name="service_account_project_id" type="text" id="service_account_project_id" value="<?php echo esc_attr( $current_settings['service_account_project_id'] ); ?>" class="regular-text" placeholder="ví dụ: my-project-123456"></td>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_private_key_id">Private Key ID</label></th>
                <td><input name="service_account_private_key_id" type="text" id="service_account_private_key_id" value="<?php echo esc_attr( $current_settings['service_account_private_key_id'] ); ?>" class="regular-text" placeholder="ví dụ: abc123def456..."></td>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_private_key">Private Key</label></th>
                <td><textarea name="service_account_private_key" id="service_account_private_key" rows="8" cols="70" class="large-text code" placeholder="-----BEGIN PRIVATE KEY-----...-----END PRIVATE KEY-----"><?php echo esc_textarea( $current_settings['service_account_private_key'] ); ?></textarea></td>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_client_email">Client Email</label></th>
                <td><input name="service_account_client_email" type="email" id="service_account_client_email" value="<?php echo esc_attr( $current_settings['service_account_client_email'] ); ?>" class="regular-text" placeholder="ví dụ: <EMAIL>"></td>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_client_id">Client ID</label></th>
                <td><input name="service_account_client_id" type="text" id="service_account_client_id" value="<?php echo esc_attr( $current_settings['service_account_client_id'] ); ?>" class="regular-text" placeholder="ví dụ: 123456789012345678901"></td>
            </tr>
            <tr>
                <th scope="row"><label for="service_account_client_x509_cert_url">Client X509 Cert URL</label></th>
                <td><input name="service_account_client_x509_cert_url" type="url" id="service_account_client_x509_cert_url" value="<?php echo esc_attr( $current_settings['service_account_client_x509_cert_url'] ); ?>" class="regular-text" placeholder="https://www.googleapis.com/robot/v1/metadata/x509/..."></td>
            </tr>

            <tr>
                <th scope="row">Cấu hình cột Google Sheet</th>
                <td>
                    <div class="sheet-column-config">
                        <!-- Hidden field to always use string method -->
                        <input type="hidden" name="column_config_method" value="string">

                        <!-- String-based Configuration (Only Method) -->
                        <div id="string-config" class="config-section" style="display: block;">
                            <div class="string-config-container" style="width: 100%; max-width: 100%; overflow: hidden; box-sizing: border-box;">
                                <!-- Column Names Section -->
                                <div class="config-input-section" style="margin-bottom: 20px; width: 100%;">
                                    <label for="column_mapping_string" class="config-label">
                                        <strong>📋 Tên các cột (Column Names)</strong>
                                    </label>
                                    <p class="description" style="margin: 8px 0 12px 0; word-wrap: break-word;">
                                        Nhập tên các cột theo định dạng: <code>TênCột1|TênCột2|TênCột3...</code><br>
                                        <em>Ví dụ:</em> <code style="word-break: break-all;">ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute</code>
                                    </p>

                                    <div class="input-with-helper" style="width: 100%;">
                                        <textarea name="column_mapping_string" id="column_mapping_string"
                                               placeholder="ID|Username|Password|Login_URL|Status|Order_ID|Sold_Date|Expiration_Date|Platform|Plan|Price|Payment_Status|Notes|Variation_Attribute"
                                               rows="3"
                                               style="width: 100%; max-width: 100%; box-sizing: border-box; font-family: 'Courier New', monospace; font-size: 13px; resize: vertical; padding: 8px; border: 1px solid #ddd; border-radius: 4px; word-wrap: break-word; overflow-wrap: break-word;"><?php echo esc_attr($current_settings['column_mapping_string'] ?? ''); ?></textarea>

                                        <div class="helper-buttons" style="margin-top: 8px; display: flex; flex-wrap: wrap; gap: 8px;">
                                            <button type="button" id="add-column-btn" class="button button-secondary">
                                                ➕ Thêm cột
                                            </button>
                                            <button type="button" id="reset-default-btn" class="button button-secondary">
                                                🔄 Reset mặc định
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Column Indices Section -->
                                <div class="column-indices-section" style="margin-bottom: 20px; width: 100%;">
                                    <label class="config-label">
                                        <strong>🔢 Chỉ số cột (Column Indices)</strong>
                                    </label>
                                    <p class="description" style="margin: 8px 0 12px 0;">
                                        Nhập chỉ số cột tương ứng với từng tên cột ở trên (A=0, B=1, C=2, D=3...)
                                    </p>

                                    <textarea name="column_indices_string" id="column_indices_string"
                                           placeholder="0|1|2|3|4|5|6|7|8|9|10|11|12|13"
                                           rows="3"
                                           style="width: 100%; max-width: 100%; box-sizing: border-box; font-family: 'Courier New', monospace; font-size: 13px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"><?php
                                           // Use saved column_indices_string if available, otherwise generate from columns
                                           if (!empty($current_settings['column_indices_string'])) {
                                               echo esc_attr($current_settings['column_indices_string']);
                                           } else {
                                               $indices = array_values($current_settings['columns']);
                                               echo esc_attr(implode('|', $indices));
                                           }
                                           ?></textarea>
                                </div>

                                <!-- Preview Section -->
                                <div id="string-config-preview" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #0073aa;">
                                    <strong>📊 Preview Mapping:</strong>
                                    <div id="preview-content" style="margin-top: 10px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Responsive CSS for column configuration -->
                    <style>
                    .string-config-container {
                        width: 100% !important;
                        max-width: 100% !important;
                        overflow: hidden !important;
                        box-sizing: border-box !important;
                    }
                    .string-config-container * {
                        box-sizing: border-box !important;
                    }
                    .string-config-container textarea {
                        width: 100% !important;
                        max-width: 100% !important;
                        min-width: 0 !important;
                        word-wrap: break-word !important;
                        overflow-wrap: break-word !important;
                    }
                    .helper-buttons {
                        display: flex !important;
                        flex-wrap: wrap !important;
                        gap: 8px !important;
                    }
                    @media (max-width: 768px) {
                        .helper-buttons {
                            flex-direction: column !important;
                        }
                        .helper-buttons .button {
                            width: 100% !important;
                        }
                    }
                    </style>
                </td>
            </tr>
            <tr>
                <th scope="row">Bật Lọc Tài khoản theo Biến thể</th>
                <td>
                    <label>
                        <input name="enable_variation_matching" type="checkbox" id="enable_variation_matching" value="1" <?php checked( $current_settings['enable_variation_matching'], true ); ?>>
                        Khi được bật, plugin sẽ chỉ lấy tài khoản từ Sheet nếu giá trị trong cột "Variation Attribute" khớp với tên biến thể được mua.
                    </label>
                    <p class="description" style="color: #c00; font-weight: bold;">Yêu cầu: Bạn phải có một cột trong Sheet để chứa tên các biến thể (ví dụ: "Gói 1 tháng", "Xanh, L") và cấu hình đúng chỉ số cột "Variation Attribute" ở trên.</p>
                </td>
            </tr>

            <tr>
                <th scope="row"><label for="status_available">Trạng thái "Còn hàng"</label></th>
                <td><input name="status_available" type="text" id="status_available" value="<?php echo esc_attr( $current_settings['status_available'] ); ?>" class="regular-text"></td>
            </tr>
            <tr>
                <th scope="row"><label for="status_sold">Trạng thái "Đã bán"</label></th>
                <td><input name="status_sold" type="text" id="status_sold" value="<?php echo esc_attr( $current_settings['status_sold'] ); ?>" class="regular-text"></td>
            </tr>
            <tr>
                <th scope="row"><label for="test_log_sheet_name">Tên tab Test Log</label></th>
                <td><input name="test_log_sheet_name" type="text" id="test_log_sheet_name" value="<?php echo esc_attr( $current_settings['test_log_sheet_name'] ); ?>" class="regular-text" placeholder="Ví dụ: Test Log"></td>
            </tr>
            <tr>
                <th scope="row"><label for="shop_name">Tên cửa hàng</label></th>
                <td><input name="shop_name" type="text" id="shop_name" value="<?php echo esc_attr( $current_settings['shop_name'] ); ?>" class="regular-text"></td>
            </tr>
            <tr>
                <th scope="row"><label for="shop_logo_url">URL Logo cửa hàng</label></th>
                <td><input name="shop_logo_url" type="url" id="shop_logo_url" value="<?php echo esc_attr( $current_settings['shop_logo_url'] ); ?>" class="regular-text" placeholder="http://yourdomain.com/your-logo.png"></td>
            </tr>
            <tr>
                <th scope="row"><label for="support_url">URL Hỗ trợ / Fanpage</label></th>
                <td><input name="support_url" type="url" id="support_url" value="<?php echo esc_attr( $current_settings['support_url'] ); ?>" class="regular-text" placeholder="https://www.facebook.com/yourfanpage"></td>
            </tr>
            <tr>
                <th scope="row"><label for="support_text">Văn bản hỗ trợ</label></th>
                <td><input name="support_text" type="text" id="support_text" value="<?php echo esc_attr( $current_settings['support_text'] ); ?>" class="regular-text" placeholder="Fanpage của chúng tôi"></td>
            </tr>
            <tr>
                <th scope="row">Tắt xác minh SSL (Chỉ gỡ lỗi)</th>
                <td>
                    <label>
                        <input name="disable_ssl_verify" type="checkbox" id="disable_ssl_verify" value="1" <?php checked( $current_settings['disable_ssl_verify'], true ); ?>>
                        <span style="color: red; font-weight: bold;">CẢNH BÁO: Không nên dùng trong môi trường thật.</span>
                    </label>
                </td>
            </tr>
            <tr>
                <th scope="row">Bật Quản lý Email WooCommerce</th>
                <td>
                    <label>
                        <input name="enable_wc_email_management" type="checkbox" id="enable_wc_email_management" value="1" <?php checked( $current_settings['enable_wc_email_management'] ?? false, true ); ?>>
                        Bật tùy chọn này để kích hoạt tab "Quản lý Email WooCommerce".
                    </label>
                </td>
            </tr>
        </tbody>
    </table>
    <div style="display: flex; gap: 10px; align-items: flex-start; margin-top: 1em;">
        <p class="submit" style="margin: 0;"><input type="submit" name="accuflow_save_settings" id="submit" class="button button-primary" value="Lưu Cài đặt"></p>
        <p class="submit" style="margin: 0;"><button type="button" id="test_connection_button" class="button button-secondary">Kiểm tra Kết nối</button></p>
        <p class="submit" style="margin: 0;"><button type="button" id="detailed_diagnosis_button" class="button button-secondary">Chẩn đoán</button></p>
        <p class="submit" style="margin: 0;"><button type="button" id="download_cacert_button" class="button button-secondary">Tải CA Certs</button></p>
    </div>
</form>
<div id="diagnosis_results_box" style="border: 1px solid #ccc; padding: 15px; margin-top: 20px; display: none;">
    <strong>Kết quả chẩn đoán:</strong>
    <pre id="diagnosis_results_content"></pre>
</div>
