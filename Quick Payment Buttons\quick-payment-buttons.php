<?php
/**
 * Plugin Name: Quick Payment Button
 * Description: <PERSON><PERSON> thế nút WooCommerce bằng nút "Mua ngay" và "Thêm vào giỏ", c<PERSON> tuỳ chỉnh CSS trong admin.
 * Version: 1.4
 * Author: choacc.com
 */

if (!defined('ABSPATH')) exit;

// Enqueue CSS & JS
add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style('quick-payment-button-style', plugin_dir_url(__FILE__) . 'style.css');
    wp_enqueue_script('quick-payment-button-script', plugin_dir_url(__FILE__) . 'script.js', ['jquery'], null, true);
    wp_localize_script('quick-payment-button-script', 'qpb_data', [
        'ajax_url'      => admin_url('admin-ajax.php'),
        'nonce'         => wp_create_nonce('qpb_nonce'),
        'checkout_url'  => wc_get_checkout_url()
    ]);
});

// Inject custom CSS + ẩn nút mặc định
add_action('wp_head', function () {
    echo '<style>.single_add_to_cart_button { display: none !important; }</style>';
    $custom_css = get_option('qpb_custom_css', '');
    if (!empty($custom_css)) {
        echo '<style>' . $custom_css . '</style>';
    }
});

// Hiển thị nút thay thế
add_action('woocommerce_single_product_summary', function () {
    global $product;
    if (!$product || !$product->is_purchasable()) return;

    echo '<div class="quick-payment-buttons" data-product-id="' . esc_attr($product->get_id()) . '">
        <button class="quick-payment-buy-button">Mua ngay</button>
        <button class="quick-payment-addtocart-button">Thêm vào giỏ</button>
    </div>';
}, 31);

// AJAX: Thêm vào giỏ
add_action('wp_ajax_qpb_add_to_cart', 'qpb_add_to_cart');
add_action('wp_ajax_nopriv_qpb_add_to_cart', 'qpb_add_to_cart');
function qpb_add_to_cart() {
    check_ajax_referer('qpb_nonce', 'nonce');
    $product_id = intval($_POST['product_id']);
    if ($product_id && WC()->cart) {
        WC()->cart->add_to_cart($product_id);
        wp_send_json_success();
    }
    wp_send_json_error();
}

// AJAX: Mua ngay
add_action('wp_ajax_qpb_buy_now', 'qpb_buy_now');
add_action('wp_ajax_nopriv_qpb_buy_now', 'qpb_buy_now');
function qpb_buy_now() {
    check_ajax_referer('qpb_nonce', 'nonce');
    $product_id = intval($_POST['product_id']);
    if ($product_id && WC()->cart) {
        WC()->cart->empty_cart();
        WC()->cart->add_to_cart($product_id);
        wp_send_json_success();
    }
    wp_send_json_error();
}

// Admin menu
add_action('admin_menu', function () {
    add_menu_page('Quick Payment Button', 'Quick Payment Button', 'manage_options', 'quick-payment-button', 'qpb_admin_page');
});

// Save custom CSS
add_action('admin_post_qpb_save_css', function () {
    if (current_user_can('manage_options')) {
        $css = wp_kses_post($_POST['custom_css'] ?? '');
        update_option('qpb_custom_css', $css);
    }
    wp_redirect(admin_url('admin.php?page=quick-payment-button&updated=true'));
    exit;
});

// Trang admin
function qpb_admin_page() {
    $css = get_option('qpb_custom_css', '');
    ?>
    <div class="wrap">
        <h1>Quick Payment Button – Tuỳ chỉnh CSS</h1>
        <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
            <input type="hidden" name="action" value="qpb_save_css">
            <h2>Nhập CSS tuỳ chỉnh</h2>
            <textarea name="custom_css" style="width:100%; height:300px;"><?php echo esc_textarea($css); ?></textarea>
            <p><input type="submit" class="button button-primary" value="Lưu CSS"></p>
        </form>
    </div>
    <?php
}
