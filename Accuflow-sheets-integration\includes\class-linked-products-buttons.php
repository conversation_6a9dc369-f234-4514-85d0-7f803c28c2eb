<?php
/**
 * Linked Products Buttons Class
 *
 * @package AccuFlow_Extensions
 * @subpackage Linked_Products_Buttons
 * @since 3.0.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'Linked_Products_Buttons' ) ) {
    /**
     * Lớp Linked_Products_Buttons xử lý chức năng hiển thị sản phẩm liên quan
     */
    class Linked_Products_Buttons {

        public function __construct() {
            // Constructor
        }

        public function init() {
            // Only initialize if WooCommerce is active
            if ( ! class_exists( 'WooCommerce' ) ) {
                return;
            }

            // Check if Linked Products Buttons is enabled
            if ( ! get_option( 'lpb_enabled', 1 ) ) {
                return;
            }

            // Frontend hooks
            add_action( 'woocommerce_after_add_to_cart_button', array( $this, 'display_linked_products_buttons' ) );
            add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_styles' ) );

            // Product meta hooks
            add_action( 'woocommerce_product_options_general_product_data', array( $this, 'add_custom_name_field' ) );
            add_action( 'woocommerce_process_product_meta', array( $this, 'save_custom_name_field' ) );
        }

        public function display_linked_products_buttons() {
            global $product;
            if (!$product || !is_product()) return;
            
            $linked_ids = $product->get_cross_sell_ids();
            if (empty($linked_ids)) return;

            $group_label = get_option('choacc_linked_buttons_group_label', 'Chọn gói sản phẩm');
            echo '<div class="linked-products-group"><strong>' . esc_html($group_label) . '</strong></div>';

            echo '<div class="linked-products-buttons">';
            foreach ($linked_ids as $linked_id) {
                $linked_product = wc_get_product($linked_id);
                if (!$linked_product) continue;
                
                $custom_button_name = get_post_meta($linked_id, '_linked_button_custom_name', true);
                $button_name = $custom_button_name ? esc_html($custom_button_name) : esc_html($linked_product->get_name());
                echo '<a href="' . get_permalink($linked_id) . '" class="linked-product-btn">' . $button_name . '</a>';
            }
            echo '</div>';
        }

        public function enqueue_styles() {
            if (is_product()) {
                $default_css = get_option('choacc_linked_buttons_custom_css', '') ?: $this->get_default_css();
                
                wp_register_style('linked-products-buttons-style', false);
                wp_enqueue_style('linked-products-buttons-style');
                wp_add_inline_style('linked-products-buttons-style', $default_css);
            }
        }

        private function get_default_css() {
            return '            
                .linked-products-group {
                    font-size: 16px;
                    margin-bottom: 8px;
                    font-weight: 600;
                }
                .linked-products-buttons {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    margin-top: 5px;
                }
                .linked-product-btn {
                    display: inline-block;
                    padding: 6px 14px;
                    font-size: 13px;
                    background: #f5f5f5;
                    color: #333;
                    text-decoration: none;
                    border-radius: 5px;
                    border: 1px solid #ccc;
                    transition: 0.3s ease;
                    white-space: nowrap;
                }
                .linked-product-btn:hover {
                    background-color: #2563eb;
                    color: #fff;
                    border-color: #2563eb;
                }';
        }

        public function add_custom_name_field() {
            woocommerce_wp_text_input([
                'id' => '_linked_button_custom_name',
                'label' => 'Tên nút liên kết',
                'description' => 'Tên hiển thị trên nút liên kết (nếu để trống sẽ dùng tên sản phẩm).',
                'desc_tip' => true,
            ]);
        }

        public function save_custom_name_field($post_id) {
            $custom_name = isset($_POST['_linked_button_custom_name']) ? sanitize_text_field($_POST['_linked_button_custom_name']) : '';
            update_post_meta($post_id, '_linked_button_custom_name', $custom_name);
        }



        public function get_settings() {
            return [
                'custom_css' => get_option('choacc_linked_buttons_custom_css', ''),
                'group_label' => get_option('choacc_linked_buttons_group_label', 'Chọn gói sản phẩm')
            ];
        }

        public function save_settings($settings) {
            if (isset($settings['custom_css'])) {
                update_option('choacc_linked_buttons_custom_css', wp_unslash($settings['custom_css']));
            }
            if (isset($settings['group_label'])) {
                update_option('choacc_linked_buttons_group_label', sanitize_text_field($settings['group_label']));
            }
        }
    }
}
