jQuery(document).ready(function($) {
    // KHAI BÁO BIẾN $modal TẠI ĐÂY để nó có phạm vi toàn c<PERSON>c trong script này
    var $modal = $('#accuflow-email-settings-modal');

    // Tab functionality for main admin page
    $('.accuflow-tab-button').on('click', function() {
        var tabId = $(this).data('tab');

        $('.accuflow-tab-button').removeClass('active');
        $(this).addClass('active');

        $('.accuflow-tab-content').removeClass('active').hide();
        $('#accuflow-tab-' + tabId).addClass('active').show();

        // Load product list if on that tab and not loaded yet
        if (tabId === 'product-links' && !$('#product-list-body').data('loaded')) {
            loadProductList(accuflow_ajax_object.current_s_query, accuflow_ajax_object.current_paged);
            $('#product-list-body').data('loaded', true);
        }
        
        // Initialize color pickers when on WC Emails tab
        if (tabId === 'wc-emails') {
            // Delay initialization slightly to ensure elements are visible
            setTimeout(function() {
                $('.accuflow-color-picker').each(function() {
                    // Check if Iris has already been initialized
                    if (!$(this).data('iris')) {
                        $(this).iris({
                            hide: true,
                            palettes: true,
                            change: function(event, ui) {
                                $(this).css({ backgroundColor: ui.color.toString() });
                            }
                        });
                    }
                });
            }, 100);
        }
    });

    // Initial tab selection
    var initialTab = window.location.hash.substring(1);
    var targetTabButton = null;

    if (initialTab && $('.accuflow-tab-button[data-tab="' + initialTab + '"]').length) {
        targetTabButton = $('.accuflow-tab-button[data-tab="' + initialTab + '"]');
    } else {
        targetTabButton = $('.accuflow-tab-button[data-tab="settings"]');
    }

    if (targetTabButton) {
        targetTabButton.trigger('click');
        $('html, body').animate({ scrollTop: 0 }, 'fast');
    }

    // Function to toggle visibility of config fields for product rows
    function toggleFulfillmentConfig(productId, method) {
        var $row = $('#product-row-' + productId);
        $row.find('.accuflow-fulfillment-config').hide();
        $row.find('.accuflow-sheet-config-buttons, .accuflow-api-config-buttons').hide();

        if (method === 'google_sheet') {
            $row.find('.accuflow-sheet-config').show();
            $row.find('.accuflow-sheet-config-buttons').show();
        } else if (method === 'api_call') {
            $row.find('.accuflow-api-config').show();
            $row.find('.accuflow-api-config-buttons').show();
            var $customLinkCheckbox = $row.find('.accuflow-api-input-custom-link');
            var $regexField = $row.find('.accuflow-api-custom-link-regex-field');
            if ($customLinkCheckbox.is(':checked')) {
                $regexField.show();
            } else {
                $regexField.hide();
            }
        }
    }

    // Function to load product list via AJAX
    function loadProductList(searchQuery, pageNum) {
        var $productListBody = $('#product-list-body');
        $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span class="spinner is-active" style="float:none; vertical-align: middle;"></span> Đang tải sản phẩm...</td></tr>');

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_get_product_list_ajax',
                security: accuflow_ajax_object.get_product_list_nonce,
                s: searchQuery,
                paged: pageNum
            },
            success: function(response) {
                if (response.success) {
                    $productListBody.html(response.data.rows);
                    $('.tablenav-pages').html(response.data.pagination);

                    $('.accuflow-fulfillment-method').each(function() {
                        var productId = $(this).data('product-id');
                        var method = $(this).val();
                        toggleFulfillmentConfig(productId, method);
                    });

                } else {
                    $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span style="color: red;">Lỗi khi tải sản phẩm: ' + response.data + '</span></td></tr>');
                }
            },
            error: function() {
                $productListBody.html('<tr><td colspan="5" style="text-align:center;"><span style="color: red;">Lỗi không xác định khi tải sản phẩm.</span></td></tr>');
            },
            complete: function() {
                // Ensure all dynamically loaded elements also have correct event handlers if needed
            }
        });
    }

    // Handle change event for fulfillment method dropdown (delegated for dynamic content)
    $(document).on('change', '.accuflow-fulfillment-method', function() {
        var productId = $(this).data('product-id');
        var method = $(this).val();
        toggleFulfillmentConfig(productId, method);
    });

    // Handle change for custom link checkbox (delegated event)
    $(document).on('change', '.accuflow-api-input-custom-link', function() {
        var $regexField = $(this).closest('.accuflow-api-config').find('.accuflow-api-custom-link-regex-field');
        if ($(this).is(':checked')) {
            $regexField.slideDown();
        } else {
            $regexField.slideUp();
        }
    });

    // Test Read (Google Sheet)
    $(document).on('click', '.accuflow-sheets-test-link', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var tabGid = button.closest('tr').find('.accuflow-sheets-tab-gid').val();
        var resultSpan = $('#test-result-' + productId);

        resultSpan.html('<span class="spinner is-active"></span> Đang kiểm tra đọc...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_product_link_ajax',
                security: accuflow_ajax_object.test_product_link_nonce,
                product_id: productId,
                tab_gid: tabGid
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi kiểm tra đọc.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Write connection log (Google Sheet)
    $(document).on('click', '.accuflow-sheets-write-log-link', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var productSku = button.data('product-sku');
        var tabGid = button.closest('tr').find('.accuflow-sheets-tab-gid').val();
        var resultSpan = $('#test-result-' + productId);

        resultSpan.html('<span class="spinner is-active"></span> Đang ghi log...</span>');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_write_connection_log_ajax',
                security: accuflow_ajax_object.write_connection_log_nonce,
                product_id: productId,
                tab_gid: tabGid,
                product_sku: productSku
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi ghi log.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Test API Call (API Call)
    $(document).on('click', '.accuflow-sheets-test-api-call', function() {
        var button = $(this);
        var productId = button.data('product-id');
        var apiEndpointUrl = button.closest('tr').find('.accuflow-api-endpoint-url').val();
        var apiInputFields = [];
        button.closest('tr').find('input[name^="api_input_fields[' + productId + ']"]:checked').each(function() {
            apiInputFields.push($(this).val());
        });
        var apiCustomLinkRegex = button.closest('tr').find('input[name^="api_custom_link_regex[' + productId + ']"]').val();
        var resultSpan = $('#test-result-' + productId);

        resultSpan.html('<span class="spinner is-active"></span> Đang kiểm tra API Call...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_api_call_ajax',
                security: accuflow_ajax_object.test_api_call_nonce,
                product_id: productId,
                api_endpoint_url: apiEndpointUrl,
                api_input_fields: apiInputFields,
                api_custom_link_regex: apiCustomLinkRegex
            },
            success: function(response) {
                if (response.success) {
                    resultSpan.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultSpan.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultSpan.html('<span class="test-error">Lỗi không xác định khi kiểm tra API Call.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });


    // JSON converter
    $('#convert_json').click(function() {
        var jsonText = $('#json_converter').val().trim();

        if (!jsonText) {
            alert('Vui lòng paste nội dung JSON vào ô trên!');
            return;
        }

        try {
            var jsonData = JSON.parse(jsonText);

            if (jsonData.project_id) {
                $('#service_account_project_id').val(jsonData.project_id);
            }
            if (jsonData.private_key_id) {
                $('#service_account_private_key_id').val(jsonData.private_key_id);
            }
            if (jsonData.private_key) {
                var privateKey = jsonData.private_key.replace(/\\n/g, '\n');
                $('#service_account_private_key').val(privateKey);
            }
            if (jsonData.client_email) {
                $('#service_account_client_email').val(jsonData.client_email);
            }
            if (jsonData.client_id) {
                $('#service_account_client_id').val(jsonData.client_id);
            }
            if (jsonData.client_x509_cert_url) {
                $('#service_account_client_x509_cert_url').val(jsonData.client_x509_cert_url);
            }

            $('#json_converter').val(''); // Clear converter field
            alert('✅ Chuyển đổi thành công! Các trường đã được điền tự động. Hãy nhấn "Lưu Cài đặt" để lưu.');

            $('html, body').animate({
                scrollTop: $('#service_account_project_id').offset().top - 100
            }, 500);

        } catch (e) {
            alert('❌ Lỗi: JSON không hợp lệ. Vui lòng kiểm tra lại nội dung JSON.\n\nLỗi chi tiết: ' + e.message);
        }
    });

    // Clear fields
    $('#clear_fields').click(function() {
        if (confirm('Bạn có chắc muốn xóa tất cả các trường Service Account?')) {
            $('#service_account_project_id, #service_account_private_key_id, #service_account_private_key, #service_account_client_email, #service_account_client_id, #service_account_client_x509_cert_url, #json_converter').val('');
            alert('✅ Đã xóa tất cả các trường!');
        }
    });

    // Test connection button
    $('#test_connection_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang kiểm tra kết nối...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_test_connection_ajax',
                security: accuflow_ajax_object.test_connection_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    resultContent.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi kiểm tra kết nối.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Detailed diagnosis button
    $('#detailed_diagnosis_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang chạy chẩn đoán chi tiết...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_detailed_diagnosis_ajax',
                security: accuflow_ajax_object.detailed_diagnosis_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html(response.data);
                } else {
                    resultContent.html('<span class="test-error">Lỗi: ' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi chạy chẩn đoán.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Download CA Certificates button
    $('#download_cacert_button').on('click', function() {
        var button = $(this);
        var resultBox = $('#diagnosis_results_box');
        var resultContent = $('#diagnosis_results_content');

        resultContent.html('<span class="spinner is-active"></span> Đang tải xuống CA Certificates...');
        resultBox.fadeIn();
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_download_cacert_ajax',
                security: accuflow_ajax_object.download_cacert_nonce
            },
            success: function(response) {
                if (response.success) {
                    resultContent.html('<span class="test-success">' + response.data + '</span>');
                } else {
                    $resultContent.html('<span class="test-error">' + response.data + '</span>');
                }
            },
            error: function() {
                resultContent.html('<span class="test-error">Lỗi không xác định khi tải CA Certificates.</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Close diagnosis box
    $(document).on('click', '.button-close-diagnosis', function() {
        $('#diagnosis_results_box').fadeOut();
    });


    // Product search and pagination
    $('#product-search-button').on('click', function() {
        var searchQuery = $('#product-search-input').val();
        loadProductList(searchQuery, 1);
    });

    $(document).on('click', '.accuflow-pagination-link', function(e) {
        e.preventDefault();
        var pageNum = $(this).data('page');
        var searchQuery = $('#product-search-input').val();
        loadProductList(searchQuery, pageNum);
    });

    // --- WC Email Management Scripts ---

    // Open Email Settings Modal - event listener
    $(document).on('click', '.accuflow-open-email-settings-modal', function() {
        console.log('Nút Cài đặt đã được nhấn!'); // Added console log

        var button = $(this);
        var emailKey = button.data('email-key');
        var $row = $('tr[data-email-key="' + emailKey + '"]');

        // Lấy dữ liệu hiện tại từ hidden inputs của hàng tương ứng trong bảng chính
        var emailLabel = $row.find('strong').text();
        // Cần đảm bảo các input hidden này có giá trị đúng.
        // Lỗi ReferenceError ban đầu khiến các val() này không chạy được.
        var currentSubject = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][subject]"]').val();
        var currentHeading = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][heading]"]').val();
        var currentTemplate = $row.find('input[name="wc_email_custom_configs[' + emailKey + '][template]"]').val();

        // Lấy các giá trị cấu hình email chung (logo, màu sắc...) từ các giá trị đã được localize
        var globalSettings = accuflow_ajax_object.global_email_settings_js;

        // Điền dữ liệu vào các trường trong modal
        $('#accuflow-modal-email-label').text(emailLabel);
        $('#modal_email_subject').val(currentSubject);
        $('#modal_email_heading').val(currentHeading);
        $('#modal_email_template').val(currentTemplate);

        // Điền dữ liệu cho tab "Cài đặt Chung Email" trong modal
        $('#modal_email_header_image').val(globalSettings.email_header_image);
        $('#modal_email_header_image_width').val(globalSettings.email_header_image_width);
        $('#modal_email_header_alignment').val(globalSettings.email_header_alignment);
        $('#modal_email_font_family').val(globalSettings.email_font_family);
        // Kích hoạt Iris color picker và set màu
        $('#modal_email_base_color').val(globalSettings.email_base_color);
        $('#modal_email_background_color').val(globalSettings.email_background_color);
        $('#modal_email_body_background_color').val(globalSettings.email_body_background_color);
        $('#modal_email_text_color').val(globalSettings.email_text_color);
        $('#modal_email_footer_text_color').val(globalSettings.email_footer_text_color);
        // Sau khi set giá trị, cần gọi .iris('color', ...) để cập nhật màu hiển thị
        $('#modal_email_base_color').iris('color', globalSettings.email_base_color);
        $('#modal_email_background_color').iris('color', globalSettings.email_background_color);
        $('#modal_email_body_background_color').iris('color', globalSettings.email_body_background_color);
        $('#modal_email_text_color').iris('color', globalSettings.email_text_color);
        $('#modal_email_footer_text_color').iris('color', globalSettings.email_footer_text_color);


        $('#modal_email_footer_text').val(globalSettings.email_footer_text);
        
        // Đặt emailKey vào các nút Preview và Gửi thử trong modal
        $modal.find('.accuflow-preview-email-button').data('email-key', emailKey);
        $modal.find('.accuflow-send-test-email-button').data('email-key', emailKey);

        // Lưu emailKey vào modal để sử dụng sau khi lưu
        $modal.data('current-email-key', emailKey);

        // Reset modal tabs (mở tab chỉnh sửa template mặc định)
        $modal.find('.accuflow-modal-tab-button').removeClass('active');
        $modal.find('.accuflow-modal-tab-button[data-modal-tab="template-editor"]').addClass('active');
        $modal.find('.accuflow-modal-tab-content').removeClass('active').hide();
        $modal.find('#accuflow-modal-tab-template-editor').addClass('active').show();

        // Clear previous preview and test results
        $modal.find('.accuflow-preview-iframe').contents().find('html').empty();
        $modal.find('.accuflow-test-email-result').empty();
        $modal.find('.accuflow-email-preview-iframe-wrapper').hide(); // Hide iframe wrapper initially

        // Hiển thị modal
        $modal.fadeIn();
    });

    // Close Email Settings Modal
    $(document).on('click', '.accuflow-close-preview-modal', function() {
        $(this).closest('#accuflow-email-settings-modal').fadeOut();
    });

    // Save Individual Email Settings from Modal
    $(document).on('click', '.accuflow-save-modal-settings', function() {
        var emailKey = $modal.data('current-email-key');
        var $row = $('tr[data-email-key="' + emailKey + '"]');

        // Lấy dữ liệu từ modal
        var newSubject = $('#modal_email_subject').val();
        var newHeading = $('#modal_email_heading').val();
        var newTemplate = $('#modal_email_template').val();

        // Cập nhật giá trị vào hidden input fields trong row của bảng chính
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][subject]"]').val(newSubject);
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][heading]"]').val(newHeading);
        $row.find('input[name="wc_email_custom_configs[' + emailKey + '][template]"]').val(newTemplate);
        
        // Cập nhật hiển thị tiêu đề mặc định trong bảng (cột thứ 3)
        $row.find('td:eq(3) code').text(newSubject);

        $modal.fadeOut();
        // Hiển thị thông báo "Đã cập nhật (cần Lưu Cài đặt Email)"
        $('.accuflow-admin-page .notice').remove();
        var successNotice = '<div class="notice notice-warning is-dismissible"><p>Cài đặt email <strong>' + emailKey + '</strong> đã được cập nhật (cần nhấn nút "Lưu Cài đặt Email" để lưu hoàn toàn).</p></div>';
        $('.accuflow-admin-page h1').after(successNotice);
    });

    // Save Global Email Settings from Modal
    $(document).on('click', '.accuflow-save-modal-global-settings', function() {
        // Lấy dữ liệu từ các trường cài đặt chung trong modal
        var newHeaderImage = $('#modal_email_header_image').val();
        var newHeaderImageWidth = $('#modal_email_header_image_width').val();
        var newHeaderAlignment = $('#modal_email_header_alignment').val();
        var newFontFamily = $('#modal_email_font_family').val();
        var newBaseColor = $('#modal_email_base_color').val();
        var newBackgroundColor = $('#modal_email_background_color').val();
        var newBodyBackgroundColor = $('#modal_email_body_background_color').val();
        var newTextColor = $('#modal_email_text_color').val();
        var newFooterTextColor = $('#modal_email_footer_text_color').val();
        var newFooterText = $('#modal_email_footer_text').val();

        // Cập nhật giá trị vào các input ẩn của form chính (để khi submit sẽ gửi đi)
        $('[name="email_header_image"]').val(newHeaderImage);
        $('[name="email_header_image_width"]').val(newHeaderImageWidth);
        $('[name="email_header_alignment"]').val(newHeaderAlignment);
        $('[name="email_font_family"]').val(newFontFamily);
        $('[name="email_base_color"]').val(newBaseColor);
        $('[name="email_background_color"]').val(newBackgroundColor);
        $('[name="email_body_background_color"]').val(newBodyBackgroundColor);
        $('[name="email_text_color"]').val(newTextColor);
        $('[name="email_footer_text_color"]').val(newFooterTextColor);
        $('[name="email_footer_text"]').val(newFooterText);
        
        $modal.fadeOut();
        // Hiển thị thông báo "Đã cập nhật (cần Lưu Cài đặt Email)"
        $('.accuflow-admin-page .notice').remove();
        var successNotice = '<div class="notice notice-warning is-dismissible"><p>Cài đặt chung email đã được cập nhật (cần nhấn nút "Lưu Cài đặt Email" để lưu hoàn toàn).</p></div>';
        $('.accuflow-admin-page h1').after(successNotice);
    });


    // Handle recipient type select change (in main table)
    $(document).on('change', '.accuflow-recipient-type-select', function() {
        var $parentTd = $(this).closest('td');
        var selectedValue = $(this).val();
        if (selectedValue === 'custom_email_input') {
            $parentTd.find('.accuflow-custom-recipient-email-field').slideDown();
        } else {
            $parentTd.find('.accuflow-custom-recipient-email-field').slideUp();
        }
    });

    // Initial display for custom recipient email fields on load
    $('.accuflow-recipient-type-select').each(function() {
        if ($(this).val() === 'custom_email_input') {
            $(this).closest('td').find('.accuflow-custom-recipient-email-field').show();
        }
    });

    // Preview Email Button (inside modal)
    $(document).on('click', '.accuflow-preview-email-button', function() {
        var button = $(this);
        var emailKey = $modal.data('current-email-key'); // Lấy emailKey từ modal
        var previewType = button.data('preview-type'); // 'pc' or 'mobile'
        var $resultDiv = $modal.find('.accuflow-test-email-result');
        var $iframeWrapper = $modal.find('.accuflow-email-preview-iframe-wrapper');
        var $iframe = $iframeWrapper.find('.accuflow-preview-iframe');

        // Lấy template, subject, heading hiện tại từ modal
        var currentTemplate = $('#modal_email_template').val();
        var currentSubject = $('#modal_email_subject').val();
        var currentHeading = $('#modal_email_heading').val();

        // Lấy các giá trị cấu hình email chung từ modal (khi preview, dùng giá trị trong modal)
        var globalEmailSettings = {
            email_header_image:          $('#modal_email_header_image').val(),
            email_footer_text:           $('#modal_email_footer_text').val(),
            email_base_color:            $('#modal_email_base_color').val(),
            email_background_color:      $('#modal_email_background_color').val(),
            email_body_background_color: $('#modal_email_body_background_color').val(),
            email_text_color:            $('#modal_email_text_color').val(),
            email_footer_text_color:     $('#modal_email_footer_text_color').val(),
            email_font_family:           $('#modal_email_font_family').val(),
            email_header_image_width:    $('#modal_email_header_image_width').val(),
            email_header_alignment:      $('#modal_email_header_alignment').val()
        };


        $resultDiv.html('<span class="spinner is-active"></span> Đang tạo xem trước...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_email_preview_ajax',
                security: accuflow_ajax_object.email_preview_nonce,
                email_key: emailKey,
                preview_type: previewType,
                // Gửi template, subject, heading để preview chính xác nội dung đang soạn
                custom_template: currentTemplate,
                custom_subject: currentSubject,
                custom_heading: currentHeading,
                // Gửi các cài đặt email chung để preview chính xác
                global_email_settings: globalEmailSettings
            },
            success: function(response) {
                if (response.success) {
                    $resultDiv.html(''); // Clear previous message
                    $iframe.contents().find('html').html(response.data);
                    $iframeWrapper.show(); // Show iframe wrapper

                    if (previewType === 'mobile') {
                        $iframe.css({width: '320px', height: '568px'}); // Standard mobile dimensions
                        $iframe.parent().css({maxWidth: '340px', margin: '20px auto'}); // Container for mobile
                    } else {
                        $iframe.css({width: '100%', height: '80vh'}); // Full width for PC
                        $iframe.parent().css({maxWidth: '800px', margin: '20px auto'}); // Container for PC
                    }

                } else {
                    $resultDiv.html('<span class="test-error">❌ Lỗi: ' + response.data + '</span>');
                    $iframeWrapper.hide();
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $resultDiv.html('<span class="test-error">❌ Lỗi không xác định khi tạo xem trước: ' + textStatus + ' - ' + errorThrown + '</span>');
                $iframeWrapper.hide();
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });


    // Send Test Email Button (inside modal)
    $(document).on('click', '.accuflow-send-test-email-button', function() {
        var button = $(this);
        var emailKey = $modal.data('current-email-key'); // Lấy emailKey từ modal
        var recipientEmail = $modal.find('.accuflow-test-email-recipient').val(); // Lấy từ input trong modal
        var $resultDiv = $modal.find('.accuflow-test-email-result');

        // Lấy template, subject, heading hiện tại từ modal
        var currentTemplate = $('#modal_email_template').val();
        var currentSubject = $('#modal_email_subject').val();
        var currentHeading = $('#modal_email_heading').val();

        // Lấy các giá trị cấu hình email chung từ modal (khi gửi thử, dùng giá trị trong modal)
        var globalEmailSettings = {
            email_header_image:          $('#modal_email_header_image').val(),
            email_footer_text:           $('#modal_email_footer_text').val(),
            email_base_color:            $('#modal_email_base_color').val(),
            email_background_color:      $('#modal_email_background_color').val(),
            email_body_background_color: $('#modal_email_body_background_color').val(),
            email_text_color:            $('#modal_email_text_color').val(),
            email_footer_text_color:     $('#modal_email_footer_text_color').val(),
            email_font_family:           $('#modal_email_font_family').val(),
            email_header_image_width:    $('#modal_email_header_image_width').val(),
            email_header_alignment:      $('#modal_email_header_alignment').val()
        };


        if (!recipientEmail) {
            $resultDiv.html('<span class="test-error">Vui lòng nhập địa chỉ email người nhận thử nghiệm.</span>');
            return;
        }
        if (!isValidEmail(recipientEmail)) {
            $resultDiv.html('<span class="test-error">Địa chỉ email người nhận không hợp lệ.</span>');
            return;
        }


        $resultDiv.html('<span class="spinner is-active"></span> Đang gửi email thử...');
        button.prop('disabled', true);

        $.ajax({
            url: accuflow_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'accuflow_send_test_email_ajax',
                security: accuflow_ajax_object.send_test_email_nonce,
                email_key: emailKey,
                recipient_email: recipientEmail,
                // Gửi template, subject, heading để gửi email thử chính xác nội dung đang soạn
                custom_template: currentTemplate,
                custom_subject: currentSubject,
                custom_heading: currentHeading,
                // Gửi các cài đặt email chung để gửi thử chính xác
                global_email_settings: globalEmailSettings
            },
            success: function(response) {
                if (response.success) {
                    $resultDiv.html('<span class="test-success">✅ ' + response.data + '</span>');
                } else {
                    $resultDiv.html('<span class="test-error">❌ ' + response.data + '</span>');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $resultDiv.html('<span class="test-error">❌ Lỗi không xác định khi gửi email thử: ' + textStatus + ' - ' + errorThrown + '</span>');
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Modal Tabs functionality
    $(document).on('click', '.accuflow-modal-tab-button', function() {
        var tabId = $(this).data('modal-tab');
        // $modal đã được khai báo ở đầu ready scope

        $modal.find('.accuflow-modal-tab-button').removeClass('active');
        $(this).addClass('active');

        $modal.find('.accuflow-modal-tab-content').removeClass('active').hide();
        $modal.find('#accuflow-modal-tab-' + tabId).addClass('active').show();

        // Initialize color pickers if on global settings tab
        if (tabId === 'global-settings') {
            setTimeout(function() { // Delay to ensure elements are rendered
                $('.accuflow-color-picker').each(function() {
                    if (!$(this).data('iris')) { // Only initialize if not already
                        $(this).iris({
                            hide: true,
                            palettes: true,
                            change: function(event, ui) {
                                $(this).css({ backgroundColor: ui.color.toString() });
                            }
                        });
                    }
                });
            }, 100);
        }
    });

    // Basic email validation
    function isValidEmail(email) {
        var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        return regex.test(email);
    }
});