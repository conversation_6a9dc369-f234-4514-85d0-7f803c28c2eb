jQuery(function($){
  const $btns = $('.quick-payment-buttons');

  function refreshCartIcon() {
    if (typeof wc_cart_fragments_params !== 'undefined') {
      $(document.body).trigger('wc_fragment_refresh');
    }
  }

  $btns.on('click', '.quick-payment-buy-button', function(){
    const pid = $btns.data('product-id');
    $.post(qpb_data.ajax_url, {
      action: 'qpb_buy_now',
      nonce: qpb_data.nonce,
      product_id: pid
    }, res => {
      if (res.success) {
        window.location.href = qpb_data.checkout_url;
      } else {
        alert('Không thể mua ngay!');
      }
    });
  });

  $btns.on('click', '.quick-payment-addtocart-button', function(){
    const pid = $btns.data('product-id');
    $.post(qpb_data.ajax_url, {
      action: 'qpb_add_to_cart',
      nonce: qpb_data.nonce,
      product_id: pid
    }, res => {
      if (res.success) {
        alert('Đã thêm vào giỏ!');
        refreshCartIcon(); // 🔄 cập nhật biểu tượng giỏ hàng
      } else {
        alert('Lỗi khi thêm!');
      }
    });
  });
});
