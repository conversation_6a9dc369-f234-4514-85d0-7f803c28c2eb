<?php
/**
 * Quick Payment Buttons Admin Display
 *
 * @package AccuFlow_Extensions
 * @subpackage Admin
 * @since 3.0.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Xử lý lưu settings
if (isset($_POST['save_quick_payment_settings'])) {
    check_admin_referer('quick_payment_settings_nonce');
    
    $custom_css = isset($_POST['qpb_custom_css']) ? wp_unslash($_POST['qpb_custom_css']) : '';
    update_option('qpb_custom_css', $custom_css);
    
    echo '<div class="notice notice-success is-dismissible"><p>Cài đặt Quick Payment Buttons đã được lưu thành công!</p></div>';
}

$current_css = get_option('qpb_custom_css', '');
?>

<div class="wrap">
    <h2>Quick Payment Buttons Settings</h2>
    <p>Tùy chỉnh nút thanh toán nhanh cho sản phẩm WooCommerce. Plugin sẽ thay thế nút "Thêm vào giỏ hàng" mặc định bằng 2 nút: "Mua ngay" và "Thêm vào giỏ".</p>
    
    <form method="post" action="">
        <?php wp_nonce_field('quick_payment_settings_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="qpb_custom_css">CSS Tùy chỉnh</label>
                </th>
                <td>
                    <textarea name="qpb_custom_css" id="qpb_custom_css" rows="15" cols="80" class="large-text code"><?php echo esc_textarea($current_css); ?></textarea>
                    <p class="description">
                        Nhập CSS tùy chỉnh để thay đổi giao diện của các nút. Để trống để sử dụng style mặc định.
                    </p>
                </td>
            </tr>
        </table>
        
        <?php submit_button('Lưu cài đặt', 'primary', 'save_quick_payment_settings'); ?>
    </form>
    
    <hr>
    
    <h3>CSS Mặc định</h3>
    <p>Dưới đây là CSS mặc định được sử dụng cho các nút:</p>
    <pre style="background: #f1f1f1; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>.quick-payment-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.quick-payment-buy-button,
.quick-payment-addtocart-button {
  padding: 12px 26px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-payment-buy-button {
  background-color: #1a73e8;
  color: white;
}

.quick-payment-buy-button:hover {
  background-color: #0c5fd0;
}

.quick-payment-addtocart-button {
  background: white;
  color: #1a73e8;
  border: 2px solid #1a73e8;
}

.quick-payment-addtocart-button:hover {
  background-color: #f0f8ff;
}</code></pre>
    
    <h3>Hướng dẫn sử dụng</h3>
    <ul>
        <li><strong>Nút "Mua ngay":</strong> Xóa giỏ hàng hiện tại, thêm sản phẩm và chuyển thẳng đến trang thanh toán</li>
        <li><strong>Nút "Thêm vào giỏ":</strong> Thêm sản phẩm vào giỏ hàng và hiển thị thông báo</li>
        <li><strong>Tùy chỉnh CSS:</strong> Bạn có thể thay đổi màu sắc, kích thước, font chữ của các nút bằng cách nhập CSS tùy chỉnh</li>
        <li><strong>Responsive:</strong> Các nút tự động điều chỉnh trên mobile với flex-wrap</li>
    </ul>
</div>
