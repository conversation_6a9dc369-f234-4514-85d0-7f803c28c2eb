<?php
/*
Plugin Name: Choacc.com - Linked Products Buttons
Description: <PERSON><PERSON><PERSON> thị sản phẩm bán ch<PERSON>o (cross-sell) dưới dạng nút bấm đơn giản trên trang sản phẩm.
Version: 1.5
Author: Choacc.com
*/

if (!defined('ABSPATH')) exit;

add_action('woocommerce_after_add_to_cart_button', 'custom_linked_products_buttons_plugin');
function custom_linked_products_buttons_plugin() {
    global $product;
    if (!$product || !is_product()) return;
    $linked_ids = $product->get_cross_sell_ids();
    if (empty($linked_ids)) return;

    $group_label = get_option('choacc_linked_buttons_group_label', 'Chọn gói sản phẩm');
    echo '<div class="linked-products-group"><strong>' . esc_html($group_label) . '</strong></div>';

    echo '<div class="linked-products-buttons">';
    foreach ($linked_ids as $linked_id) {
        $linked_product = wc_get_product($linked_id);
        if (!$linked_product) continue;
        $custom_button_name = get_post_meta($linked_id, '_linked_button_custom_name', true);
        $button_name = $custom_button_name ? esc_html($custom_button_name) : esc_html($linked_product->get_name());
        echo '<a href="' . get_permalink($linked_id) . '" class="linked-product-btn">' . $button_name . '</a>';
    }
    echo '</div>';
}

add_action('wp_enqueue_scripts', 'enqueue_linked_products_buttons_styles');
function enqueue_linked_products_buttons_styles() {
    if (is_product()) {
        $default_css = get_option('choacc_linked_buttons_custom_css', '') ?: '            
            .linked-products-group {
                font-size: 16px;
                margin-bottom: 8px;
                font-weight: 600;
            }
            .linked-products-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 5px;
            }
            .linked-product-btn {
                display: inline-block;
                padding: 6px 14px;
                font-size: 13px;
                background: #f5f5f5;
                color: #333;
                text-decoration: none;
                border-radius: 5px;
                border: 1px solid #ccc;
                transition: 0.3s ease;
                white-space: nowrap;
            }
            .linked-product-btn:hover {
                background-color: #2563eb;
                color: #fff;
                border-color: #2563eb;
            }';

        wp_register_style('linked-products-buttons-style', false);
        wp_enqueue_style('linked-products-buttons-style');
        wp_add_inline_style('linked-products-buttons-style', $default_css);
    }
}

add_action('woocommerce_product_options_general_product_data', 'add_linked_button_custom_name_field');
function add_linked_button_custom_name_field() {
    woocommerce_wp_text_input([
        'id' => '_linked_button_custom_name',
        'label' => 'Tên nút liên kết',
        'description' => 'Tên hiển thị trên nút liên kết (nếu để trống sẽ dùng tên sản phẩm).',
        'desc_tip' => true,
    ]);
}

add_action('woocommerce_process_product_meta', 'save_linked_button_custom_name_field');
function save_linked_button_custom_name_field($post_id) {
    $custom_name = isset($_POST['_linked_button_custom_name']) ? sanitize_text_field($_POST['_linked_button_custom_name']) : '';
    update_post_meta($post_id, '_linked_button_custom_name', $custom_name);
}

add_action('admin_menu', 'choacc_linked_buttons_menu');
function choacc_linked_buttons_menu() {
    add_menu_page(
        'Choacc Buttons',
        'Choacc Buttons',
        'manage_options',
        'choacc-linked-buttons',
        'choacc_linked_buttons_settings_page',
        'dashicons-screenoptions'
    );
}

function choacc_linked_buttons_settings_page() {
    if (isset($_POST['choacc_css']) || isset($_POST['choacc_group_label'])) {
        check_admin_referer('choacc_save_css');
        if (isset($_POST['choacc_css'])) {
            update_option('choacc_linked_buttons_custom_css', wp_unslash($_POST['choacc_css']));
            echo '<div class="updated"><p>Lưu CSS thành công!</p></div>';
        }
        if (isset($_POST['choacc_group_label'])) {
            update_option('choacc_linked_buttons_group_label', sanitize_text_field($_POST['choacc_group_label']));
            echo '<div class="updated"><p>Lưu tiêu đề nhóm thành công!</p></div>';
        }
    }

    $saved_css = esc_textarea(get_option('choacc_linked_buttons_custom_css', ''));
    $group_label = esc_attr(get_option('choacc_linked_buttons_group_label', 'Chọn gói sản phẩm'));

    echo '<div class="wrap">';
    echo '<h1>Tuỳ chỉnh Choacc Buttons</h1>';
    echo '<form method="post">';
    wp_nonce_field('choacc_save_css');

    echo '<h2>Tiêu đề nhóm nút</h2>';
    echo '<input type="text" name="choacc_group_label" style="width: 300px;" value="' . $group_label . '">';

    echo '<h2 style="margin-top: 20px;">CSS tuỳ chỉnh</h2>';
    echo '<textarea name="choacc_css" style="width:100%; height:200px;">' . $saved_css . '</textarea>';
    submit_button('Lưu thiết lập');

    echo '<hr><h2>Hướng dẫn sử dụng</h2>';
    echo '<ul>';
    echo '<li>1. Vào sản phẩm bạn muốn làm nút → điền vào "Tên nút liên kết".</li>';
    echo '<li>2. Trong sản phẩm chính → Thêm các sản phẩm đó vào mục "Bán chéo".</li>';
    echo '<li>3. Có thể đổi tiêu đề nhóm hiển thị và CSS ngay tại đây.</li>';
    echo '</ul>';
    echo '</form></div>';
}
