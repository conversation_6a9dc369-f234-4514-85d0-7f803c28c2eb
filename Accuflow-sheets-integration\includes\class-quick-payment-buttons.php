<?php
/**
 * Quick Payment Buttons Class
 *
 * @package AccuFlow_Extensions
 * @subpackage Quick_Payment_Buttons
 * @since 3.0.0
 */

// Ngăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'Quick_Payment_Buttons' ) ) {
    /**
     * Lớp Quick_Payment_Buttons xử lý chức năng nút thanh toán nhanh
     */
    class Quick_Payment_Buttons {

        public function __construct() {
            // Constructor
        }

        public function init() {
            // Enqueue CSS & JS
            add_action('wp_enqueue_scripts', [$this, 'enqueue_scripts']);
            
            // Inject custom CSS + ẩn nút mặc định
            add_action('wp_head', [$this, 'inject_custom_css']);
            
            // Hiển thị nút thay thế
            add_action('woocommerce_single_product_summary', [$this, 'display_quick_payment_buttons'], 31);
            
            // AJAX handlers are handled by admin class
            
            // Admin hooks
            if (is_admin()) {
                add_action('admin_menu', [$this, 'add_admin_menu']);
                add_action('admin_init', [$this, 'register_settings']);
            }
        }

        public function enqueue_scripts() {
            wp_enqueue_style('quick-payment-button-style', ACCUFLOW_PLUGIN_URL . 'assets/css/quick-payment-buttons.css', [], ACCUFLOW_VERSION);
            wp_enqueue_script('quick-payment-button-script', ACCUFLOW_PLUGIN_URL . 'assets/js/quick-payment-buttons.js', ['jquery'], ACCUFLOW_VERSION, true);
            wp_localize_script('quick-payment-button-script', 'qpb_data', [
                'ajax_url'      => admin_url('admin-ajax.php'),
                'nonce'         => wp_create_nonce('qpb_nonce'),
                'checkout_url'  => wc_get_checkout_url()
            ]);
        }

        public function inject_custom_css() {
            echo '<style>.single_add_to_cart_button { display: none !important; }</style>';
            $custom_css = get_option('qpb_custom_css', '');
            if (!empty($custom_css)) {
                echo '<style>' . $custom_css . '</style>';
            }
        }

        public function display_quick_payment_buttons() {
            global $product;
            if (!$product || !$product->is_purchasable()) return;

            echo '<div class="quick-payment-buttons" data-product-id="' . esc_attr($product->get_id()) . '">
                <button class="quick-payment-buy-button">Mua ngay</button>
                <button class="quick-payment-addtocart-button">Thêm vào giỏ</button>
            </div>';
        }

        public function ajax_add_to_cart() {
            check_ajax_referer('qpb_nonce', 'nonce');
            $product_id = intval($_POST['product_id']);
            if ($product_id && WC()->cart) {
                WC()->cart->add_to_cart($product_id);
                wp_send_json_success();
            } else {
                wp_send_json_error();
            }
        }

        public function ajax_buy_now() {
            check_ajax_referer('qpb_nonce', 'nonce');
            $product_id = intval($_POST['product_id']);
            if ($product_id && WC()->cart) {
                WC()->cart->empty_cart();
                WC()->cart->add_to_cart($product_id);
                wp_send_json_success(['redirect' => wc_get_checkout_url()]);
            } else {
                wp_send_json_error();
            }
        }

        public function add_admin_menu() {
            // This will be handled by the main admin class
        }

        public function register_settings() {
            register_setting('qpb_settings', 'qpb_custom_css');
        }

        public function get_settings() {
            return [
                'custom_css' => get_option('qpb_custom_css', '')
            ];
        }

        public function save_settings($settings) {
            if (isset($settings['custom_css'])) {
                update_option('qpb_custom_css', sanitize_textarea_field($settings['custom_css']));
            }
        }
    }
}
