<?php
/**
 * Quick Payment Buttons Class
 *
 * @package AccuFlow_Extensions
 * @subpackage Quick_Payment_Buttons
 * @since 3.0.0
 */

// <PERSON>ăn chặn truy cập trực tiếp vào tệp
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( ! class_exists( 'Quick_Payment_Buttons' ) ) {
    /**
     * Lớp Quick_Payment_Buttons xử lý chức năng nút thanh toán nhanh
     */
    class Quick_Payment_Buttons {

        public function __construct() {
            // Constructor
        }

        public function init() {
            // Only initialize if WooCommerce is active
            if ( ! class_exists( 'WooCommerce' ) ) {
                return;
            }

            // Enqueue CSS & JS
            add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );

            // Inject custom CSS + ẩn nút mặc định
            add_action( 'wp_head', array( $this, 'inject_custom_css' ) );

            // <PERSON><PERSON><PERSON> thị nút thay thế
            add_action( 'woocommerce_single_product_summary', array( $this, 'display_quick_payment_buttons' ), 31 );

            // AJAX handlers
            add_action( 'wp_ajax_qpb_add_to_cart', array( $this, 'ajax_add_to_cart' ) );
            add_action( 'wp_ajax_nopriv_qpb_add_to_cart', array( $this, 'ajax_add_to_cart' ) );
            add_action( 'wp_ajax_qpb_buy_now', array( $this, 'ajax_buy_now' ) );
            add_action( 'wp_ajax_nopriv_qpb_buy_now', array( $this, 'ajax_buy_now' ) );
        }

        public function enqueue_scripts() {
            // Only enqueue on product pages
            if ( ! is_product() ) {
                return;
            }

            wp_enqueue_style(
                'quick-payment-button-style',
                ACCUFLOW_PLUGIN_URL . 'assets/css/quick-payment-buttons.css',
                array(),
                ACCUFLOW_VERSION
            );

            wp_enqueue_script(
                'quick-payment-button-script',
                ACCUFLOW_PLUGIN_URL . 'assets/js/quick-payment-buttons.js',
                array( 'jquery' ),
                ACCUFLOW_VERSION,
                true
            );

            wp_localize_script( 'quick-payment-button-script', 'qpb_data', array(
                'ajax_url'      => admin_url( 'admin-ajax.php' ),
                'nonce'         => wp_create_nonce( 'qpb_nonce' ),
                'checkout_url'  => function_exists( 'wc_get_checkout_url' ) ? wc_get_checkout_url() : ''
            ));
        }

        public function inject_custom_css() {
            // Only on product pages
            if ( ! is_product() ) {
                return;
            }

            // Ẩn nút WooCommerce mặc định
            echo '<style type="text/css">';
            echo '.single_add_to_cart_button { display: none !important; }';

            // Thêm CSS tùy chỉnh nếu có
            $custom_css = get_option( 'qpb_custom_css', '' );
            if ( ! empty( $custom_css ) ) {
                echo wp_strip_all_tags( $custom_css );
            }
            echo '</style>';
        }

        public function display_quick_payment_buttons() {
            global $product;

            // Kiểm tra product có tồn tại và có thể mua không
            if ( ! $product || ! $product->is_purchasable() || ! $product->is_in_stock() ) {
                return;
            }

            // Kiểm tra WooCommerce functions
            if ( ! function_exists( 'wc_get_checkout_url' ) ) {
                return;
            }

            $product_id = $product->get_id();

            echo '<div class="quick-payment-buttons" data-product-id="' . esc_attr( $product_id ) . '">';
            echo '<button type="button" class="quick-payment-buy-button">Mua ngay</button>';
            echo '<button type="button" class="quick-payment-addtocart-button">Thêm vào giỏ</button>';
            echo '</div>';
        }

        public function ajax_add_to_cart() {
            // Verify nonce
            if ( ! wp_verify_nonce( $_POST['nonce'], 'qpb_nonce' ) ) {
                wp_send_json_error( array( 'message' => 'Security check failed' ) );
                return;
            }

            $product_id = intval( $_POST['product_id'] );

            if ( $product_id && function_exists( 'WC' ) && WC()->cart ) {
                $result = WC()->cart->add_to_cart( $product_id );
                if ( $result ) {
                    wp_send_json_success( array( 'message' => 'Product added to cart' ) );
                } else {
                    wp_send_json_error( array( 'message' => 'Failed to add product to cart' ) );
                }
            } else {
                wp_send_json_error( array( 'message' => 'Invalid product or cart not available' ) );
            }
        }

        public function ajax_buy_now() {
            // Verify nonce
            if ( ! wp_verify_nonce( $_POST['nonce'], 'qpb_nonce' ) ) {
                wp_send_json_error( array( 'message' => 'Security check failed' ) );
                return;
            }

            $product_id = intval( $_POST['product_id'] );

            if ( $product_id && function_exists( 'WC' ) && WC()->cart && function_exists( 'wc_get_checkout_url' ) ) {
                WC()->cart->empty_cart();
                $result = WC()->cart->add_to_cart( $product_id );
                if ( $result ) {
                    wp_send_json_success( array( 'redirect' => wc_get_checkout_url() ) );
                } else {
                    wp_send_json_error( array( 'message' => 'Failed to add product to cart' ) );
                }
            } else {
                wp_send_json_error( array( 'message' => 'Invalid product or cart not available' ) );
            }
        }



        public function get_settings() {
            return [
                'custom_css' => get_option('qpb_custom_css', '')
            ];
        }

        public function save_settings($settings) {
            if (isset($settings['custom_css'])) {
                update_option('qpb_custom_css', sanitize_textarea_field($settings['custom_css']));
            }
        }
    }
}
